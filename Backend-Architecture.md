# Laravel 12 Backend Architecture — Premarital Course Platform

> Target: High-performance, secure, and developer-friendly Laravel 12 API backend with Filament Admin panel that integrates with PostgreSQL, Paymob, Cloudflare R2, and Bunny Stream to power the Next.js frontend.

---

## 1) High-Level Architecture

**Style:** Modular Laravel application with clear domain boundaries using Laravel Sanctum for API authentication and Filament Admin for church management.

**Core Laravel Modules:**

- **Auth Module** – Laravel Sanctum authentication, email verification, password resets, device sessions
- **User Module** – profiles, avatars, churches directory, account management
- **Course Module** – lectures, transcripts, content gating/ordering
- **Progress Module** – watch progress, bookmarks, time tracking, completion rules
- **Assessment Module** – quizzes, final exams, attempts, autosave, analytics
- **Payment Module** – Paymob integration, webhooks, receipts, refunds
- **Certificate Module** – eligibility, PDF + QR generation, verification endpoints
- **File Module** – uploads, secure access, virus scan hooks, image processing, signed URLs (R<PERSON> & <PERSON>)
- **Notification Module** – email + (optional) SMS, in-app notifications, scheduled nudges
- **Admin Module** – Filament Admin panel with RBAC, reports, dashboards, bulk ops, audit logs
- **Realtime Module** – WebSocket/Pusher for live progress & notifications
- **Analytics Module** – aggregation jobs, metrics export
- **System Module** – health checks, config, feature flags

**Cross-cutting:**

- **ORM:** Eloquent (PostgreSQL)
- **Cache/Queues:** Redis + Laravel Queue (jobs: emails, certificates, analytics)
- **Storage:** Cloudflare R2 (S3-compatible) for PDFs & uploads; Bunny Stream for videos
- **Admin Panel:** Filament Admin for church management
- **Docs & Versioning:** Laravel Scribe (OpenAPI), API versioning (`/v1`) via routes
- **Observability:** Laravel Telescope, Sentry (errors), metrics, Uptime Kuma

---

## 2) Project Structure

```
mashora-backend/
 ├─ app/
 │   ├─ Http/
 │   │   ├─ Controllers/
 │   │   │   ├─ Api/
 │   │   │   │   ├─ V1/
 │   │   │   │   │   ├─ AuthController.php
 │   │   │   │   │   ├─ UserController.php
 │   │   │   │   │   ├─ CourseController.php
 │   │   │   │   │   ├─ LectureController.php
 │   │   │   │   │   ├─ ProgressController.php
 │   │   │   │   │   ├─ QuizController.php
 │   │   │   │   │   ├─ PaymentController.php
 │   │   │   │   │   ├─ CertificateController.php
 │   │   │   │   │   └─ FileController.php
 │   │   │   │   └─ Admin/
 │   │   │   │       ├─ DashboardController.php
 │   │   │   │       ├─ UserManagementController.php
 │   │   │   │       └─ AnalyticsController.php
 │   │   │   └─ Web/
 │   │   │       └─ HealthController.php
 │   │   ├─ Middleware/
 │   │   │   ├─ Sanctum.php
 │   │   │   ├─ RateLimiting.php
 │   │   │   ├─ AuditLog.php
 │   │   │   └─ FeatureFlag.php
 │   │   ├─ Requests/
 │   │   │   ├─ Auth/
 │   │   │   ├─ User/
 │   │   │   ├─ Course/
 │   │   │   └─ Payment/
 │   │   └─ Resources/
 │   │       ├─ UserResource.php
 │   │       ├─ CourseResource.php
 │   │       └─ PaymentResource.php
 │   ├─ Models/
 │   │   ├─ User.php
 │   │   ├─ UserProfile.php
 │   │   ├─ Course.php
 │   │   ├─ Lecture.php
 │   │   ├─ Quiz.php
 │   │   ├─ Question.php
 │   │   ├─ LectureProgress.php
 │   │   ├─ QuizAttempt.php
 │   │   ├─ FinalExamAttempt.php
 │   │   ├─ Certificate.php
 │   │   ├─ PaymentTransaction.php
 │   │   ├─ PaymentReceipt.php
 │   │   ├─ FileUpload.php
 │   │   ├─ AuditLog.php
 │   │   └─ FeatureFlag.php
 │   ├─ Services/
 │   │   ├─ PaymentService.php
 │   │   ├─ CertificateService.php
 │   │   ├─ FileService.php
 │   │   ├─ BunnyStreamService.php
 │   │   ├─ R2StorageService.php
 │   │   └─ NotificationService.php
 │   ├─ Jobs/
 │   │   ├─ SendWelcomeEmail.php
 │   │   ├─ GenerateCertificate.php
 │   │   ├─ SendReceiptEmail.php
 │   │   ├─ ScanFileForVirus.php
 │   │   └─ ProcessAnalytics.php
 │   ├─ Mail/
 │   │   ├─ WelcomeEmail.php
 │   │   ├─ ReceiptEmail.php
 │   │   └─ CertificateReadyEmail.php
 │   ├─ Notifications/
 │   │   ├─ CourseCompleted.php
 │   │   ├─ PaymentReceived.php
 │   │   └─ CertificateReady.php
 │   ├─ Filament/
 │   │   ├─ Resources/
 │   │   │   ├─ UserResource.php
 │   │   │   ├─ CourseResource.php
 │   │   │   ├─ PaymentResource.php
 │   │   │   ├─ CertificateResource.php
 │   │   │   └─ AnalyticsResource.php
 │   │   ├─ Pages/
 │   │   │   ├─ Dashboard.php
 │   │   │   ├─ UserAnalytics.php
 │   │   │   └─ PaymentReports.php
 │   │   └─ Widgets/
 │   │       ├─ UserStatsWidget.php
 │   │       ├─ PaymentStatsWidget.php
 │   │       └─ CourseProgressWidget.php
 │   └─ Providers/
 │       ├─ AppServiceProvider.php
 │       ├─ AuthServiceProvider.php
 │       ├─ RouteServiceProvider.php
 │       └─ FilamentServiceProvider.php
 ├─ config/
 │   ├─ auth.php
 │   ├─ sanctum.php
 │   ├─ queue.php
 │   ├─ cache.php
 │   ├─ filesystems.php
 │   ├─ mail.php
 │   └─ services.php
 ├─ database/
 │   ├─ migrations/
 │   ├─ seeders/
 │   └─ factories/
 ├─ routes/
 │   ├─ api.php
 │   ├─ web.php
 │   └─ admin.php
 ├─ storage/
 │   ├─ app/
 │   ├─ framework/
 │   └─ logs/
 ├─ tests/
 │   ├─ Feature/
 │   ├─ Unit/
 │   └─ Admin/
 ├─ resources/
 │   ├─ views/
 │   │   ├─ emails/
 │   │   └─ certificates/
 │   └─ lang/
 │       ├─ ar/
 │       └─ en/
 ├─ artisan
 ├─ composer.json
 └─ .env
```

---

## 3) Database Design (Eloquent Migrations)

> Mirrors your SQL spec; simplified for space. Add indexes & constraints as noted.

```php
// database/migrations/2024_01_01_000001_create_users_table.php
public function up()
{
    Schema::create('users', function (Blueprint $table) {
        $table->id();
        $table->string('email')->unique();
        $table->string('phone')->nullable()->unique();
        $table->string('password');
        $table->string('first_name');
        $table->string('last_name');
        $table->string('national_id')->nullable()->unique();
        $table->date('date_of_birth')->nullable();
        $table->text('address')->nullable();
        $table->string('avatar_url')->nullable();
        $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
        $table->boolean('email_verified')->default(false);
        $table->timestamp('email_verified_at')->nullable();
        $table->timestamp('deleted_at')->nullable(); // Soft delete support
        $table->timestamp('last_login_at')->nullable();
        $table->integer('login_count')->default(0);
        $table->timestamps();

        $table->index(['email']);
        $table->index(['phone']);
        $table->index(['national_id']);
        $table->index(['email_verified', 'status']);
        $table->index(['deleted_at']);
    });
}

// database/migrations/2024_01_01_000002_create_user_profiles_table.php
public function up()
{
    Schema::create('user_profiles', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('church_belong')->nullable();
        $table->string('father_of_confession')->nullable();
        $table->string('confession_church')->nullable();
        $table->string('payment_method')->nullable();
        $table->string('payment_status')->nullable(); // synced with latest paid transaction
        $table->integer('registration_step')->nullable(); // multi-step signup progress
        $table->timestamps();
    });
}

// database/migrations/2024_01_01_000003_create_lectures_table.php
public function up()
{
    Schema::create('lectures', function (Blueprint $table) {
        $table->id();
        $table->string('title');
        $table->string('title_en')->nullable();
        $table->string('title_ar')->nullable(); // Arabic title
        $table->integer('duration')->nullable(); // seconds
        $table->text('description')->nullable();
        $table->text('description_ar')->nullable(); // Arabic description
        $table->string('video_url')->nullable(); // Bunny Stream playback URL or ID
        $table->text('transcript')->nullable();
        $table->text('transcript_ar')->nullable(); // Arabic transcript
        $table->integer('order_index')->default(0);
        $table->enum('status', ['draft', 'published', 'archived'])->default('published');
        $table->timestamps();

        $table->index(['status', 'order_index']);
    });
}

// database/migrations/2024_01_01_000004_create_quizzes_table.php
public function up()
{
    Schema::create('quizzes', function (Blueprint $table) {
        $table->id();
        $table->foreignId('lecture_id')->constrained()->onDelete('cascade');
        $table->string('title');
        $table->integer('time_limit')->nullable(); // seconds
        $table->integer('passing_score')->default(70);
        $table->enum('status', ['draft', 'active', 'inactive'])->default('active');
        $table->timestamps();
    });
}

// database/migrations/2024_01_01_000005_create_questions_table.php
public function up()
{
    Schema::create('questions', function (Blueprint $table) {
        $table->id();
        $table->foreignId('quiz_id')->constrained()->onDelete('cascade');
        $table->text('text');
        $table->text('text_ar')->nullable(); // Arabic question text
        $table->json('options_json');
        $table->json('options_json_ar')->nullable(); // Arabic options
        $table->string('correct_answer');
        $table->text('explanation')->nullable();
        $table->text('explanation_ar')->nullable(); // Arabic explanation
        $table->enum('difficulty', ['easy', 'medium', 'hard'])->nullable();
        $table->timestamps();
    });
}

// database/migrations/2024_01_01_000006_create_lecture_progress_table.php
public function up()
{
    Schema::create('lecture_progress', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->foreignId('lecture_id')->constrained()->onDelete('cascade');
        $table->integer('watch_time')->default(0);
        $table->integer('completion_percentage')->default(0);
        $table->boolean('is_completed')->default(false);
        $table->timestamp('started_at')->useCurrent();
        $table->timestamp('completed_at')->nullable();
        $table->integer('bookmark_seconds')->nullable(); // last position
        $table->timestamps();

        $table->unique(['user_id', 'lecture_id']);
        $table->index(['user_id', 'completed_at']);
        $table->index(['lecture_id', 'is_completed']);
    });
}

// database/migrations/2024_01_01_000007_create_quiz_attempts_table.php
public function up()
{
    Schema::create('quiz_attempts', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->foreignId('quiz_id')->constrained()->onDelete('cascade');
        $table->json('answers_json');
        $table->integer('score');
        $table->boolean('passed');
        $table->integer('attempt_no')->default(1);
        $table->timestamp('started_at')->useCurrent();
        $table->timestamp('completed_at')->nullable();
        $table->timestamps();

        $table->unique(['user_id', 'quiz_id', 'attempt_no']);
        $table->index(['user_id', 'quiz_id', 'attempt_no']);
        $table->index(['completed_at']);
    });
}

// database/migrations/2024_01_01_000008_create_final_exam_attempts_table.php
public function up()
{
    Schema::create('final_exam_attempts', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->json('answers_json');
        $table->integer('score');
        $table->boolean('passed');
        $table->integer('attempt_no')->default(1);
        $table->json('confidence')->nullable();
        $table->json('flagged')->nullable();
        $table->timestamp('started_at')->useCurrent();
        $table->timestamp('completed_at')->nullable();
        $table->timestamps();

        $table->unique(['user_id', 'attempt_no']);
    });
}

// database/migrations/2024_01_01_000009_create_certificates_table.php
public function up()
{
    Schema::create('certificates', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('certificate_id')->unique(); // public ID
        $table->string('grade')->nullable();
        $table->integer('grade_percentage')->nullable();
        $table->timestamp('issue_date')->useCurrent();
        $table->string('validation_code')->unique();
        $table->string('qr_code_url')->nullable();
        $table->string('pdf_url')->nullable();
        $table->timestamps();
    });
}

// database/migrations/2024_01_01_000010_create_payment_transactions_table.php
public function up()
{
    Schema::create('payment_transactions', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('transaction_id')->unique(); // Paymob ID
        $table->decimal('amount', 10, 2);
        $table->string('currency')->default('EGP');
        $table->string('payment_method');
        $table->enum('status', ['pending', 'completed', 'failed', 'refunded'])->default('pending');
        $table->string('card_last4')->nullable();
        $table->string('card_type')->nullable();
        $table->timestamps();

        $table->index(['status', 'created_at']);
        $table->index(['user_id', 'created_at']);
    });
}

// database/migrations/2024_01_01_000011_create_payment_receipts_table.php
public function up()
{
    Schema::create('payment_receipts', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('transaction_id')->unique();
        $table->string('receipt_number')->unique();
        $table->decimal('amount', 10, 2);
        $table->string('currency')->default('EGP');
        $table->string('payment_method');
        $table->string('card_last4')->nullable();
        $table->string('card_type')->nullable();
        $table->enum('status', ['pending', 'completed', 'failed'])->default('completed');
        $table->string('subscription_type')->default('full');
        $table->boolean('fees_included')->default(true);
        $table->string('student_name'); // اسم الطالب
        $table->string('course_name'); // اسم الكورس
        $table->string('course_name_ar')->nullable(); // Arabic course name
        $table->text('fees_description')->nullable(); // تفاصيل الرسوم
        $table->timestamp('transaction_date')->nullable();
        $table->timestamps();

        $table->index(['user_id', 'created_at']);
        $table->index(['receipt_number']);
    });
}

// database/migrations/2024_01_01_000012_create_file_uploads_table.php
public function up()
{
    Schema::create('file_uploads', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('file_type');
        $table->string('original_name');
        $table->string('stored_name');
        $table->string('file_path')->unique();
        $table->bigInteger('file_size');
        $table->string('mime_type');
        $table->enum('virus_scan_status', ['pending', 'clean', 'infected'])->default('pending');
        $table->json('virus_scan_result')->nullable();
        $table->string('checksum_md5')->nullable();
        $table->string('checksum_sha256')->nullable();
        $table->json('allowed_file_types'); // whitelist
        $table->timestamp('upload_date')->useCurrent();
        $table->timestamps();

        $table->index(['virus_scan_status']);
        $table->index(['user_id', 'upload_date']);
    });
}

// database/migrations/2024_01_01_000013_create_audit_logs_table.php
public function up()
{
    Schema::create('audit_logs', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
        $table->string('action'); // "CREATE", "UPDATE", "DELETE"
        $table->string('entity'); // "User", "Payment", etc.
        $table->string('entity_id');
        $table->json('old_values')->nullable();
        $table->json('new_values')->nullable();
        $table->string('ip_address')->nullable();
        $table->text('user_agent')->nullable();
        $table->timestamps();

        $table->index(['user_id', 'created_at']);
        $table->index(['entity', 'entity_id']);
        $table->index(['created_at']);
    });
}

// database/migrations/2024_01_01_000014_create_feature_flags_table.php
public function up()
{
    Schema::create('feature_flags', function (Blueprint $table) {
        $table->id();
        $table->string('name')->unique();
        $table->string('display_name');
        $table->boolean('enabled')->default(false);
        $table->text('description')->nullable();
        $table->json('conditions')->nullable(); // user segments, percentage rollout
        $table->timestamps();

        $table->index(['enabled']);
    });
}
```

> Performance indexes have been added throughout the schema for optimal query performance.

---

## 4) API Design — Laravel Routes

**Auth Routes (Sanctum)**

```php
// routes/api.php
Route::prefix('v1')->group(function () {
    // Public routes
    Route::post('/auth/signup', [AuthController::class, 'signup']);
    Route::post('/auth/signin', [AuthController::class, 'signin']);
    Route::post('/auth/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/auth/reset-password', [AuthController::class, 'resetPassword']);
    Route::get('/auth/verify-email/{token}', [AuthController::class, 'verifyEmail']);
    Route::post('/auth/resend-verification', [AuthController::class, 'resendVerification']);

    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/auth/signout', [AuthController::class, 'signout']);
        Route::post('/auth/refresh', [AuthController::class, 'refresh']);

        // User routes
        Route::get('/user/profile', [UserController::class, 'profile']);
        Route::put('/user/profile', [UserController::class, 'updateProfile']);
        Route::post('/user/avatar', [UserController::class, 'uploadAvatar']);
        Route::put('/user/password', [UserController::class, 'updatePassword']);
        Route::get('/user/churches', [UserController::class, 'churches']);
        Route::get('/user/progress', [UserController::class, 'progress']);
        Route::get('/user/statistics', [UserController::class, 'statistics']);

        // Course routes
        Route::get('/lectures/{lecture}', [LectureController::class, 'show']);
        Route::post('/lectures/{lecture}/progress', [LectureController::class, 'updateProgress']);
        Route::get('/lectures/{lecture}/transcript', [LectureController::class, 'transcript']);
        Route::post('/lectures/{lecture}/complete', [LectureController::class, 'complete']);
        Route::get('/lectures/{lecture}/video', [LectureController::class, 'video']);
        Route::post('/lectures/{lecture}/bookmark', [LectureController::class, 'bookmark']);

        // Assessment routes
        Route::get('/quiz/{lecture}', [QuizController::class, 'show']);
        Route::post('/quiz/{lecture}/start', [QuizController::class, 'start']);
        Route::post('/quiz/{lecture}/save', [QuizController::class, 'save']);
        Route::post('/quiz/{lecture}/submit', [QuizController::class, 'submit']);
        Route::get('/quiz/{lecture}/results', [QuizController::class, 'results']);
        Route::post('/quiz/{lecture}/retake', [QuizController::class, 'retake']);

        // Final exam routes
        Route::get('/final-exam', [FinalExamController::class, 'show']);
        Route::post('/final-exam/start', [FinalExamController::class, 'start']);
        Route::post('/final-exam/auto-save', [FinalExamController::class, 'autoSave']);
        Route::post('/final-exam/submit', [FinalExamController::class, 'submit']);
        Route::get('/final-exam/results', [FinalExamController::class, 'results']);
        Route::post('/final-exam/flag-question', [FinalExamController::class, 'flagQuestion']);
        Route::post('/final-exam/set-confidence', [FinalExamController::class, 'setConfidence']);

        // Payment routes
        Route::post('/payment/initialize', [PaymentController::class, 'initialize']);
        Route::post('/payment/process', [PaymentController::class, 'process']);
        Route::get('/payment/status/{id}', [PaymentController::class, 'status']);
        Route::post('/payment/verify', [PaymentController::class, 'verify']);
        Route::get('/payment/methods', [PaymentController::class, 'methods']);

        // Receipt routes
        Route::get('/receipt/{transactionId}', [ReceiptController::class, 'show']);
        Route::get('/receipt/{id}/download', [ReceiptController::class, 'download']);
        Route::post('/receipt/email', [ReceiptController::class, 'email']);

        // Certificate routes
        Route::get('/certificate/eligibility', [CertificateController::class, 'eligibility']);
        Route::post('/certificate/generate', [CertificateController::class, 'generate']);
        Route::get('/certificate/download', [CertificateController::class, 'download']);
        Route::get('/certificate/verify/{certificateId}', [CertificateController::class, 'verify']);

        // File routes
        Route::post('/upload/avatar', [FileController::class, 'uploadAvatar']);
        Route::post('/upload/documents', [FileController::class, 'uploadDocuments']);
        Route::get('/files/{id}', [FileController::class, 'show']);
        Route::delete('/files/{id}', [FileController::class, 'destroy']);
        Route::get('/files/user/{userId}', [FileController::class, 'userFiles']);

        // Dashboard routes
        Route::get('/dashboard', [DashboardController::class, 'index']);
        Route::post('/user/activity', [DashboardController::class, 'logActivity']);
        Route::get('/user/recommendations', [DashboardController::class, 'recommendations']);
    });

    // Webhook routes (no auth required)
    Route::post('/payment/webhook', [PaymentController::class, 'webhook']);

    // Public certificate verification
    Route::get('/certificate/verify/{certificateId}', [CertificateController::class, 'verify']);
});

// Admin routes
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'admin'])->group(function () {
    Route::get('/users', [Admin\UserController::class, 'index']);
    Route::get('/users/{user}', [Admin\UserController::class, 'show']);
    Route::put('/users/{user}', [Admin\UserController::class, 'update']);
    Route::delete('/users/{user}', [Admin\UserController::class, 'destroy']);
    Route::post('/users/bulk-action', [Admin\UserController::class, 'bulkAction']);

    Route::put('/lectures/{lecture}', [Admin\LectureController::class, 'update']);
    Route::post('/quizzes', [Admin\QuizController::class, 'store']);

    Route::get('/analytics', [Admin\AnalyticsController::class, 'index']);
    Route::post('/certificates/bulk', [Admin\CertificateController::class, 'bulkGenerate']);
    Route::get('/dashboard', [Admin\DashboardController::class, 'index']);
    Route::get('/reports', [Admin\ReportController::class, 'index']);
    Route::get('/logs', [Admin\LogController::class, 'index']);
    Route::get('/statistics', [Admin\StatisticsController::class, 'index']);

    // Feature flags
    Route::get('/feature-flags', [Admin\FeatureFlagController::class, 'index']);
    Route::post('/feature-flags', [Admin\FeatureFlagController::class, 'store']);
    Route::put('/feature-flags/{flag}', [Admin\FeatureFlagController::class, 'update']);
    Route::delete('/feature-flags/{flag}', [Admin\FeatureFlagController::class, 'destroy']);

    // Audit logs
    Route::get('/audit-logs', [Admin\AuditLogController::class, 'index']);
    Route::get('/audit-logs/{entity}/{entityId}', [Admin\AuditLogController::class, 'show']);

    // File security
    Route::get('/files/quarantine', [Admin\FileController::class, 'quarantine']);
    Route::post('/files/scan/{id}', [Admin\FileController::class, 'scan']);
    Route::get('/files/scan-status/{id}', [Admin\FileController::class, 'scanStatus']);
});

// Health check routes
Route::get('/health', [HealthController::class, 'index']);
Route::get('/health/db', [HealthController::class, 'database']);
Route::get('/health/redis', [HealthController::class, 'redis']);
Route::get('/health/external', [HealthController::class, 'external']);
```

---

## 5) Laravel Models & Eloquent Relationships

```php
// app/Models/User.php
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'email', 'phone', 'password', 'first_name', 'last_name',
        'national_id', 'date_of_birth', 'address', 'avatar_url',
        'status', 'email_verified', 'email_verified_at'
    ];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'date_of_birth' => 'date',
        'deleted_at' => 'datetime',
        'last_login_at' => 'datetime',
    ];

    // Relationships
    public function profile()
    {
        return $this->hasOne(UserProfile::class);
    }

    public function progress()
    {
        return $this->hasMany(LectureProgress::class);
    }

    public function quizAttempts()
    {
        return $this->hasMany(QuizAttempt::class);
    }

    public function finalAttempts()
    {
        return $this->hasMany(FinalExamAttempt::class);
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    public function payments()
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    public function receipts()
    {
        return $this->hasMany(PaymentReceipt::class);
    }

    public function uploads()
    {
        return $this->hasMany(FileUpload::class);
    }

    public function auditLogs()
    {
        return $this->hasMany(AuditLog::class);
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function getIsAdminAttribute()
    {
        return $this->role === 'admin';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeVerified($query)
    {
        return $query->where('email_verified', true);
    }

    // Methods
    public function canAccessLecture($lectureId)
    {
        // Check if user has completed previous lecture
        $lecture = Lecture::find($lectureId);
        if (!$lecture || $lecture->order_index === 0) {
            return true;
        }

        $previousLecture = Lecture::where('order_index', $lecture->order_index - 1)->first();
        if (!$previousLecture) {
            return true;
        }

        return $this->progress()
            ->where('lecture_id', $previousLecture->id)
            ->where('is_completed', true)
            ->exists();
    }

    public function hasPaid()
    {
        return $this->payments()
            ->where('status', 'completed')
            ->exists();
    }

    public function isEligibleForCertificate()
    {
        return $this->hasPaid() &&
               $this->progress()->where('is_completed', true)->count() === Lecture::count() &&
               $this->finalAttempts()->where('passed', true)->exists();
    }
}

// app/Models/Lecture.php
class Lecture extends Model
{
    use HasFactory;

    protected $fillable = [
        'title', 'title_en', 'title_ar', 'duration', 'description',
        'description_ar', 'video_url', 'transcript', 'transcript_ar',
        'order_index', 'status'
    ];

    protected $casts = [
        'duration' => 'integer',
        'order_index' => 'integer',
    ];

    // Relationships
    public function quiz()
    {
        return $this->hasOne(Quiz::class);
    }

    public function progress()
    {
        return $this->hasMany(LectureProgress::class);
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order_index');
    }

    // Methods
    public function getTitleAttribute($value)
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->title_ar ? $this->title_ar : $value;
    }

    public function getDescriptionAttribute($value)
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->description_ar ? $this->description_ar : $value;
    }

    public function getTranscriptAttribute($value)
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->transcript_ar ? $this->transcript_ar : $value;
    }
}

// app/Models/PaymentTransaction.php
class PaymentTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'transaction_id', 'amount', 'currency',
        'payment_method', 'status', 'card_last4', 'card_type'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function receipt()
    {
        return $this->hasOne(PaymentReceipt::class, 'transaction_id', 'transaction_id');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    // Methods
    public function markAsCompleted()
    {
        $this->update(['status' => 'completed']);

        // Update user profile payment status
        $this->user->profile()->update(['payment_status' => 'paid']);

        // Create receipt
        $this->createReceipt();

        // Send notifications
        $this->user->notify(new PaymentReceived($this));

        // Dispatch certificate generation job if eligible
        if ($this->user->isEligibleForCertificate()) {
            GenerateCertificate::dispatch($this->user);
        }
    }

    private function createReceipt()
    {
        PaymentReceipt::create([
            'user_id' => $this->user_id,
            'transaction_id' => $this->transaction_id,
            'receipt_number' => 'RCP-' . date('Y') . '-' . str_pad($this->id, 6, '0', STR_PAD_LEFT),
            'amount' => $this->amount,
            'currency' => $this->currency,
            'payment_method' => $this->payment_method,
            'card_last4' => $this->card_last4,
            'card_type' => $this->card_type,
            'status' => 'completed',
            'student_name' => $this->user->full_name,
            'course_name' => 'كورس الإعداد للزواج',
            'course_name_ar' => 'كورس الإعداد للزواج',
            'fees_description' => 'شامل جميع الرسوم',
            'transaction_date' => now(),
        ]);
    }
}
```

---

## 6) Laravel Services & Integrations

### 6.1 Paymob Integration

```php
// app/Services/PaymentService.php
class PaymentService
{
    protected $http;
    protected $apiKey;
    protected $hmacSecret;

    public function __construct()
    {
        $this->http = Http::withHeaders([
            'Content-Type' => 'application/json',
        ]);
        $this->apiKey = config('services.paymob.api_key');
        $this->hmacSecret = config('services.paymob.hmac_secret');
    }

    public function initialize(User $user, float $amount)
    {
        // 1. Get authentication token
        $authToken = $this->getAuthToken();

        // 2. Create order
        $order = $this->createOrder($authToken, $amount);

        // 3. Create payment key
        $paymentKey = $this->createPaymentKey($authToken, $order['id'], $user, $amount);

        // 4. Store pending transaction
        $transaction = PaymentTransaction::create([
            'user_id' => $user->id,
            'transaction_id' => $order['id'],
            'amount' => $amount,
            'currency' => 'EGP',
            'payment_method' => 'card',
            'status' => 'pending',
        ]);

        return [
            'payment_key' => $paymentKey['token'],
            'iframe_url' => config('services.paymob.iframe_url'),
            'transaction_id' => $transaction->transaction_id,
        ];
    }

    public function verifyWebhook(array $payload, string $signature)
    {
        // Verify HMAC signature
        $calculatedSignature = hash_hmac('sha512', json_encode($payload), $this->hmacSecret);

        if (!hash_equals($calculatedSignature, $signature)) {
            throw new \Exception('Invalid webhook signature');
        }

        $transaction = PaymentTransaction::where('transaction_id', $payload['order']['id'])->first();

        if (!$transaction) {
            throw new \Exception('Transaction not found');
        }

        if ($payload['success'] && $payload['is_3d_secure'] === false) {
            $transaction->markAsCompleted();
        } elseif ($payload['success'] && $payload['is_3d_secure'] === true) {
            // Handle 3D Secure flow
            $transaction->update(['status' => 'pending_3ds']);
        } else {
            $transaction->update(['status' => 'failed']);
        }

        return $transaction;
    }

    private function getAuthToken()
    {
        $response = $this->http->post('https://accept.paymob.com/api/auth/tokens', [
            'api_key' => $this->apiKey,
        ]);

        return $response->json('token');
    }

    private function createOrder($authToken, $amount)
    {
        $response = $this->http->post('https://accept.paymob.com/api/ecommerce/orders', [
            'auth_token' => $authToken,
            'delivery_needed' => false,
            'amount_cents' => $amount * 100, // Convert to cents
            'currency' => 'EGP',
        ]);

        return $response->json();
    }

    private function createPaymentKey($authToken, $orderId, User $user, $amount)
    {
        $response = $this->http->post('https://accept.paymob.com/api/acceptance/payment_keys', [
            'auth_token' => $authToken,
            'amount_cents' => $amount * 100,
            'expiration' => 3600,
            'order_id' => $orderId,
            'billing_data' => [
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'phone_number' => $user->phone,
                'country' => 'EG',
                'currency' => 'EGP',
            ],
            'currency' => 'EGP',
            'integration_id' => config('services.paymob.integration_id'),
            'lock_order_when_paid' => true,
        ]);

        return $response->json();
    }
}
```

### 6.2 Cloudflare R2 Storage Service

```php
// app/Services/R2StorageService.php
class R2StorageService
{
    protected $s3Client;
    protected $bucket;
    protected $publicUrl;

    public function __construct()
    {
        $this->s3Client = new S3Client([
            'version' => 'latest',
            'region' => 'auto',
            'endpoint' => config('filesystems.disks.r2.endpoint'),
            'credentials' => [
                'key' => config('filesystems.disks.r2.key'),
                'secret' => config('filesystems.disks.r2.secret'),
            ],
        ]);

        $this->bucket = config('filesystems.disks.r2.bucket');
        $this->publicUrl = config('filesystems.disks.r2.url');
    }

    public function uploadFile($file, $path, $visibility = 'private')
    {
        $result = $this->s3Client->putObject([
            'Bucket' => $this->bucket,
            'Key' => $path,
            'Body' => $file,
            'ContentType' => $file->getMimeType(),
            'ACL' => $visibility,
        ]);

        return $result['ObjectURL'];
    }

    public function getSignedUrl($path, $expires = 3600)
    {
        $command = $this->s3Client->getCommand('GetObject', [
            'Bucket' => $this->bucket,
            'Key' => $path,
        ]);

        $request = $this->s3Client->createPresignedRequest($command, "+{$expires} seconds");

        return (string) $request->getUri();
    }

    public function deleteFile($path)
    {
        return $this->s3Client->deleteObject([
            'Bucket' => $this->bucket,
            'Key' => $path,
        ]);
    }

    public function generatePdfUrl($certificateId)
    {
        $path = "certificates/{$certificateId}.pdf";
        return $this->getSignedUrl($path, 86400); // 24 hours
    }
}
```

### 6.3 Bunny Stream Service

```php
// app/Services/BunnyStreamService.php
class BunnyStreamService
{
    protected $apiKey;
    protected $libraryId;
    protected $signingKey;

    public function __construct()
    {
        $this->apiKey = config('services.bunny.api_key');
        $this->libraryId = config('services.bunny.library_id');
        $this->signingKey = config('services.bunny.signing_key');
    }

    public function getPlaybackUrl($videoId, $userId = null)
    {
        $token = $this->generateToken($videoId, $userId);

        return "https://video.bunnycdn.com/{$this->libraryId}/{$videoId}/play.mp4?token={$token}";
    }

    public function uploadVideo($file, $title)
    {
        $response = Http::withHeaders([
            'AccessKey' => $this->apiKey,
        ])->attach('video', $file->get(), $file->getClientOriginalName())
          ->post("https://video.bunnycdn.com/library/{$this->libraryId}/videos", [
              'title' => $title,
          ]);

        return $response->json();
    }

    private function generateToken($videoId, $userId = null)
    {
        $payload = [
            'videoId' => $videoId,
            'expires' => time() + 3600, // 1 hour
        ];

        if ($userId) {
            $payload['userId'] = $userId;
        }

        $data = base64_encode(json_encode($payload));
        $signature = hash_hmac('sha256', $data, $this->signingKey);

        return $data . '.' . $signature;
    }
}
```

---

## 8) Filament Admin Panel

```php
// app/Filament/Resources/UserResource.php
class UserResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'User Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('first_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('last_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone')
                    ->tel()
                    ->maxLength(255),
                Forms\Components\Select::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'suspended' => 'Suspended',
                    ])
                    ->required(),
                Forms\Components\Toggle::make('email_verified')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('full_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'gray',
                        'suspended' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status'),
                Tables\Filters\TernaryFilter::make('email_verified'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ]);
    }
}
```

---

## 9) Configuration & Environment

```php
// config/services.php
return [
    'paymob' => [
        'api_key' => env('PAYMOB_API_KEY'),
        'hmac_secret' => env('PAYMOB_HMAC_SECRET'),
        'integration_id' => env('PAYMOB_INTEGRATION_ID'),
        'iframe_id' => env('PAYMOB_IFRAME_ID'),
    ],

    'bunny' => [
        'api_key' => env('BUNNY_API_KEY'),
        'library_id' => env('BUNNY_LIBRARY_ID'),
        'signing_key' => env('BUNNY_SIGNING_KEY'),
    ],

    'r2' => [
        'key' => env('R2_ACCESS_KEY'),
        'secret' => env('R2_SECRET_KEY'),
        'bucket' => env('R2_BUCKET'),
        'endpoint' => env('R2_ENDPOINT'),
    ],
];
```

**Environment Variables (.env):**

```bash
# Core Application
APP_NAME="Mashora Courses"
APP_ENV=production
APP_KEY=base64:your-app-key
APP_DEBUG=false
APP_URL=https://api.yourdomain.com
APP_TIMEZONE=Africa/Cairo
APP_LOCALE=ar

# Database
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=mashora_courses
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>

# Sanctum
SANCTUM_STATEFUL_DOMAINS=localhost:3000,yourdomain.com

# Paymob
PAYMOB_API_KEY=your-paymob-api-key
PAYMOB_HMAC_SECRET=your-paymob-hmac-secret
PAYMOB_INTEGRATION_ID=your-integration-id
PAYMOB_IFRAME_ID=your-iframe-id

# Bunny Stream
BUNNY_API_KEY=your-bunny-api-key
BUNNY_LIBRARY_ID=your-library-id
BUNNY_SIGNING_KEY=your-signing-key

# Cloudflare R2
R2_ACCESS_KEY=your-r2-access-key
R2_SECRET_KEY=your-r2-secret-key
R2_BUCKET=mashora-courses
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com

# Filament Admin
FILAMENT_PATH=admin
```

---

## 10) Installation & Setup

```bash
# 1. Create new Laravel project
composer create-project laravel/laravel mashora-backend

# 2. Install required packages
composer require laravel/sanctum
composer require filament/filament
composer require aws/aws-sdk-php
composer require guzzlehttp/guzzle
composer require barryvdh/laravel-dompdf
composer require simplesoftwareio/simple-qrcode
composer require spatie/laravel-permission
composer require spatie/laravel-activitylog

# 3. Install Filament Admin
php artisan filament:install

# 4. Run migrations
php artisan migrate

# 5. Create admin user
php artisan make:filament-user

# 6. Set up queue worker
php artisan queue:work

# 7. Set up scheduled tasks
php artisan schedule:work
```

---

## 11) Security & Middleware

```php
// app/Http/Middleware/AuditLog.php
class AuditLog
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        if ($request->user() && $this->shouldLog($request)) {
            AuditLog::create([
                'user_id' => $request->user()->id,
                'action' => $request->method(),
                'entity' => $this->getEntity($request),
                'entity_id' => $this->getEntityId($request),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
        }

        return $response;
    }
}
```

---

## 12) Testing Strategy

```php
// tests/Feature/AuthTest.php
class AuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_signup()
    {
        $response = $this->postJson('/api/v1/auth/signup', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }
}
```

---

## 13) Deployment & Production

**Docker Setup:**

```dockerfile
# Dockerfile
FROM php:8.2-fpm

RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev zip unzip

RUN docker-php-ext-install pdo pdo_pgsql mbstring exif pcntl bcmath gd

COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

WORKDIR /var/www
COPY . .

RUN composer install --optimize-autoloader --no-dev
RUN chown -R www-data:www-data /var/www

EXPOSE 9000
CMD ["php-fpm"]
```

---

## 14) Roadmap & Implementation Order

1. **Bootstrap Laravel Project** - Setup Laravel 12 with Sanctum and Filament
2. **Database & Migrations** - Create all database tables with relationships
3. **Authentication System** - Implement Sanctum authentication
4. **User Management** - User profiles with Filament admin
5. **Course & Lecture System** - Course structure and content management
6. **Progress Tracking** - Watch progress and completion logic
7. **Assessment System** - Quizzes and final exams
8. **Payment Integration** - Paymob integration with webhooks
9. **File Management** - Upload handling and R2 storage
10. **Certificate System** - PDF generation and verification
11. **Email System** - Queue-based email sending
12. **Admin Panel** - Complete Filament admin with analytics
13. **Security & Audit** - Audit logging and security middleware
14. **Testing & Documentation** - Comprehensive testing
15. **Deployment** - Production deployment with monitoring

---

## 15) Advanced Features (Future)

- **Multi-tenant Support** - Multiple diocese support
- **Advanced Analytics** - Learning analytics with AI insights
- **Live Streaming** - Real-time lecture streaming
- **Mobile App** - React Native mobile application
- **Advanced Security** - Two-factor authentication
- **Performance Optimization** - CDN integration
- **Compliance** - GDPR compliance tools
- **Integration** - Calendar and video conferencing tools

This Laravel 12 architecture provides a robust, scalable foundation for your premarital course platform with comprehensive admin capabilities through Filament and seamless integration with your Next.js frontend.

## 7) Laravel Jobs & Queue System

```php
// app/Jobs/GenerateCertificate.php
class GenerateCertificate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user;
    public $timeout = 300; // 5 minutes

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function handle(CertificateService $certificateService, R2StorageService $r2Service)
    {
        // Generate certificate ID
        $certificateId = 'CERT-' . strtoupper(Str::random(8));

        // Create certificate record
        $certificate = Certificate::create([
            'user_id' => $this->user->id,
            'certificate_id' => $certificateId,
            'grade' => $this->calculateGrade(),
            'grade_percentage' => $this->calculateGradePercentage(),
            'validation_code' => Str::random(16),
        ]);

        // Generate PDF
        $pdfPath = $certificateService->generatePdf($this->user, $certificate);

        // Upload to R2
        $pdfUrl = $r2Service->uploadFile(
            Storage::disk('local')->path($pdfPath),
            "certificates/{$certificateId}.pdf"
        );

        // Generate QR code
        $qrCodeUrl = $certificateService->generateQrCode($certificate);

        // Update certificate
        $certificate->update([
            'pdf_url' => $pdfUrl,
            'qr_code_url' => $qrCodeUrl,
        ]);

        // Send notification
        $this->user->notify(new CertificateReady($certificate));

        // Clean up temporary file
        Storage::disk('local')->delete($pdfPath);
    }

    private function calculateGrade()
    {
        $finalExam = $this->user->finalAttempts()->latest()->first();
        $score = $finalExam ? $finalExam->score : 0;

        if ($score >= 90) return 'A+';
        if ($score >= 80) return 'A';
        if ($score >= 70) return 'B';
        if ($score >= 60) return 'C';
        return 'D';
    }

    private function calculateGradePercentage()
    {
        $finalExam = $this->user->finalAttempts()->latest()->first();
        return $finalExam ? $finalExam->score : 0;
    }
}

// app/Jobs/SendWelcomeEmail.php
class SendWelcomeEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user;
    public $locale;

    public function __construct(User $user, $locale = 'ar')
    {
        $this->user = $user;
        $this->locale = $locale;
    }

    public function handle()
    {
        Mail::to($this->user->email)
            ->locale($this->locale)
            ->send(new WelcomeEmail($this->user));
    }
}

// app/Jobs/ScanFileForVirus.php
class ScanFileForVirus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $fileUpload;
    public $timeout = 60;

    public function __construct(FileUpload $fileUpload)
    {
        $this->fileUpload = $fileUpload;
    }

    public function handle()
    {
        $filePath = Storage::disk('local')->path($this->fileUpload->file_path);

        // Use ClamAV or similar virus scanner
        $scanResult = $this->scanFile($filePath);

        $this->fileUpload->update([
            'virus_scan_status' => $scanResult['clean'] ? 'clean' : 'infected',
            'virus_scan_result' => $scanResult,
        ]);

        if (!$scanResult['clean']) {
            // Move to quarantine
            $this->quarantineFile();

            // Notify admins
            Notification::route('mail', config('admin.email'))
                ->notify(new VirusDetected($this->fileUpload));
        }
    }

    private function scanFile($filePath)
    {
        // Implement virus scanning logic
        // This is a placeholder - you'd integrate with ClamAV or similar
        return [
            'clean' => true,
            'scanned_at' => now(),
            'scanner_version' => '1.0.0',
        ];
    }

    private function quarantineFile()
    {
        $quarantinePath = 'quarantine/' . basename($this->fileUpload->file_path);
        Storage::disk('local')->move($this->fileUpload->file_path, $quarantinePath);

        $this->fileUpload->update(['file_path' => $quarantinePath]);
    }
}
```

---
