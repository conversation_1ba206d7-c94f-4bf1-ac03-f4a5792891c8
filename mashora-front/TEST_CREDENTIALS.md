# Test Credentials for Mashora Courses

This document contains the mock authentication credentials for testing the application during development.

## Regular User Sign-In (`/signin`)

You can use any of these credentials to sign in as a regular user:

### Option 1: Pepo (Email)

- **Email/Username**: `<EMAIL>`
- **Password**: `pepo123`

### Option 2: Pepo (Phone)

- **Email/Username**: `01012345678`
- **Password**: `pepo123`

### Option 3: Student

- **Email/Username**: `<EMAIL>`
- **Password**: `student123`

### Option 4: Test User

- **Email/Username**: `<EMAIL>`
- **Password**: `test123`

## Admin Login (`/admin/login`)

For accessing the admin dashboard:

- **Username**: `admin`
- **Password**: `admin123`

## How It Works

1. The mock authentication system uses Next.js API routes
2. Credentials are validated against hardcoded values
3. Mock tokens are generated for session management
4. All authentication is simulated - no real database is used

## API Endpoints Created

- `/auth/signin` - Handles regular user authentication
- `/api/admin/login` - Handles admin authentication
- `/api/admin/verify` - Verifies admin tokens

## Notes

- This is for **development/testing only**
- In production, implement proper authentication with a database
- Tokens are simple mock strings, not real JWTs
- All user data is mocked and stored in memory
