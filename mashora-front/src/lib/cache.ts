// Client-side caching utilities
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5 minutes

  set(key: string, data: any, ttl: number = this.DEFAULT_TTL) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })
  }

  get(key: string) {
    const item = this.cache.get(key)
    if (!item) return null

    const isExpired = Date.now() - item.timestamp > item.ttl
    if (isExpired) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  delete(key: string) {
    this.cache.delete(key)
  }

  clear() {
    this.cache.clear()
  }

  // Clean expired items
  cleanup() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

export const cache = new CacheManager()

// Cleanup expired cache items every 5 minutes
if (typeof window !== "undefined") {
  setInterval(
    () => {
      cache.cleanup()
    },
    5 * 60 * 1000,
  )
}

// API response caching
export async function cachedFetch(url: string, options?: RequestInit, ttl?: number) {
  const cacheKey = `fetch:${url}:${JSON.stringify(options)}`

  // Try to get from cache first
  const cached = cache.get(cacheKey)
  if (cached) {
    return cached
  }

  try {
    const response = await fetch(url, options)
    const data = await response.json()

    // Cache successful responses
    if (response.ok) {
      cache.set(cacheKey, data, ttl)
    }

    return data
  } catch (error) {
    console.error("Fetch error:", error)
    throw error
  }
}

// Local storage with expiration
export const persistentCache = {
  set(key: string, data: any, ttl: number = 24 * 60 * 60 * 1000) {
    // 24 hours default
    const item = {
      data,
      timestamp: Date.now(),
      ttl,
    }
    localStorage.setItem(key, JSON.stringify(item))
  },

  get(key: string) {
    try {
      const item = localStorage.getItem(key)
      if (!item) return null

      const parsed = JSON.parse(item)
      const isExpired = Date.now() - parsed.timestamp > parsed.ttl

      if (isExpired) {
        localStorage.removeItem(key)
        return null
      }

      return parsed.data
    } catch {
      return null
    }
  },

  delete(key: string) {
    localStorage.removeItem(key)
  },

  clear() {
    localStorage.clear()
  },
}
