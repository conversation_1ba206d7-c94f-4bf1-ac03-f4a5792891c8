"use client";

import { useEffect, useState } from "react";

interface ScrollState {
  scrollY: number;
  scrollDirection: "up" | "down" | null;
  isScrolled: boolean;
  isScrolledPast: (threshold: number) => boolean;
  isMounted: boolean;
}

export function useScroll(): ScrollState {
  const [scrollY, setScrollY] = useState(0);
  const [scrollDirection, setScrollDirection] = useState<"up" | "down" | null>(
    null
  );
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    let lastScrollY = 0;
    let ticking = false;

    const updateScrollState = () => {
      const currentScrollY = window.scrollY;

      // Determine scroll direction
      if (currentScrollY > lastScrollY) {
        setScrollDirection("down");
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection("up");
      }

      // Update scroll position
      setScrollY(currentScrollY);
      setIsScrolled(currentScrollY > 0);

      lastScrollY = currentScrollY;
      ticking = false;
    };

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollState);
        ticking = true;
      }
    };

    // Initialize with current scroll position after mount
    const initialScrollY = window.scrollY;
    setScrollY(initialScrollY);
    setIsScrolled(initialScrollY > 0);
    setIsMounted(true);

    lastScrollY = initialScrollY;

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Ensure consistent behavior during hydration
  const isScrolledPast = (threshold: number) =>
    isMounted ? scrollY > threshold : false;

  return {
    scrollY: isMounted ? scrollY : 0, // Always 0 during SSR
    scrollDirection: isMounted ? scrollDirection : null,
    isScrolled: isMounted ? isScrolled : false, // Always false during SSR
    isScrolledPast,
    isMounted,
  };
}
