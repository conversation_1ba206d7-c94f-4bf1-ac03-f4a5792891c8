"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ModernNavbar } from "@/components/modern-navbar";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  ArrowRight,
  Clock,
  BookOpen,
  CheckCircle,
  FileText,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useParams } from "next/navigation";

// Mock lecture data
const mockLectures = {
  "1": {
    id: 1,
    number: "٠١",
    title: "مقدمة عن سر الزيجة",
    titleEn: "Introduction to the Sacrament of Marriage",
    duration: "45 دقيقة",
    description:
      "في هذه المحاضرة سنتعرف على المعنى الروحي والكتابي لسر الزيجة في المسيحية، وكيف ينظر الكتاب المقدس والتقليد الكنسي إلى الزواج كسر مقدس يجمع بين رجل وامرأة في وحدة روحية وجسدية.",
    videoUrl: "/placeholder-video.mp4", // This would be the actual video URL
    hasQuiz: true,
    isCompleted: false,
    transcript: `
      مرحباً بكم في المحاضرة الأولى من كورس الإعداد للزواج...
      
      سنبدأ اليوم بالحديث عن سر الزيجة من منظور مسيحي...
      
      الزواج في المسيحية ليس مجرد عقد اجتماعي، بل هو سر مقدس...
    `,
  },
  "2": {
    id: 2,
    number: "٠٢",
    title: "التواصل الفعال بين الزوجين",
    titleEn: "Effective Communication Between Spouses",
    duration: "50 دقيقة",
    description: "تعلم مهارات التواصل الصحي وحل النزاعات بطريقة بناءة",
    videoUrl: "/placeholder-video.mp4",
    hasQuiz: true,
    isCompleted: false,
    transcript: "محتوى المحاضرة الثانية...",
  },
};

export default function LecturePage() {
  const params = useParams();
  const lectureId = params.id as string;
  const lecture = mockLectures[lectureId as keyof typeof mockLectures];

  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [showTranscript, setShowTranscript] = useState(false);
  const [watchProgress, setWatchProgress] = useState(0);

  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const updateTime = () => setCurrentTime(video.currentTime);
    const updateDuration = () => setDuration(video.duration);
    const updateProgress = () => {
      const progress = (video.currentTime / video.duration) * 100;
      setWatchProgress(progress);
    };

    video.addEventListener("timeupdate", updateTime);
    video.addEventListener("timeupdate", updateProgress);
    video.addEventListener("loadedmetadata", updateDuration);

    return () => {
      video.removeEventListener("timeupdate", updateTime);
      video.removeEventListener("timeupdate", updateProgress);
      video.removeEventListener("loadedmetadata", updateDuration);
    };
  }, []);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const handleVolumeChange = (newVolume: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const handleSeek = (newTime: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  if (!lecture) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
            المحاضرة غير موجودة
          </h1>
          <Link href="/dashboard">
            <Button>العودة للوحة التحكم</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
      dir="rtl"
    >
      {/* Modern Navbar */}
      <ModernNavbar
        variant="dashboard"
        showBackButton={true}
        backHref="/dashboard"
        backLabel="العودة للوحة التحكم"
        showUserMenu={true}
        userInfo={{
          name: "أبانوب نشأت",
          email: "<EMAIL>",
          avatar: "/placeholder.svg?height=80&width=80",
        }}
      />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Lecture Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Badge className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2">
                المحاضرة {lecture.number}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-2">
                <Clock className="w-3 h-3" />
                {lecture.duration}
              </Badge>
              {lecture.isCompleted && (
                <Badge className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300">
                  <CheckCircle className="w-3 h-3 ml-1" />
                  مكتملة
                </Badge>
              )}
            </div>
            <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
              {lecture.title}
            </h1>
            <p className="text-xl text-slate-600 dark:text-slate-400 mb-4">
              {lecture.titleEn}
            </p>
            <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
              {lecture.description}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Video Player */}
            <div className="lg:col-span-2 space-y-6">
              <Card className="overflow-hidden">
                <div className="relative bg-black aspect-video">
                  <video
                    ref={videoRef}
                    className="w-full h-full"
                    poster="/placeholder.svg?height=400&width=600"
                    onPlay={() => setIsPlaying(true)}
                    onPause={() => setIsPlaying(false)}
                  >
                    <source src={lecture.videoUrl} type="video/mp4" />
                    متصفحك لا يدعم تشغيل الفيديو
                  </video>

                  {/* Video Controls Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-0 left-0 right-0 p-4 space-y-4">
                      {/* Progress Bar */}
                      <div className="flex items-center gap-3">
                        <span className="text-white text-sm font-mono">
                          {formatTime(currentTime)}
                        </span>
                        <div className="flex-1">
                          <input
                            type="range"
                            min={0}
                            max={duration || 0}
                            value={currentTime}
                            onChange={(e) => handleSeek(Number(e.target.value))}
                            className="w-full h-1 bg-white/30 rounded-lg appearance-none cursor-pointer"
                          />
                        </div>
                        <span className="text-white text-sm font-mono">
                          {formatTime(duration)}
                        </span>
                      </div>

                      {/* Control Buttons */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Button
                            onClick={togglePlay}
                            size="sm"
                            className="bg-white/20 hover:bg-white/30 text-white border-0"
                          >
                            {isPlaying ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>

                          <div className="flex items-center gap-2">
                            <Button
                              onClick={toggleMute}
                              size="sm"
                              className="bg-white/20 hover:bg-white/30 text-white border-0"
                            >
                              {isMuted ? (
                                <VolumeX className="w-4 h-4" />
                              ) : (
                                <Volume2 className="w-4 h-4" />
                              )}
                            </Button>
                            <input
                              type="range"
                              min={0}
                              max={1}
                              step={0.1}
                              value={isMuted ? 0 : volume}
                              onChange={(e) =>
                                handleVolumeChange(Number(e.target.value))
                              }
                              className="w-20 h-1 bg-white/30 rounded-lg appearance-none cursor-pointer"
                            />
                          </div>
                        </div>

                        <Button
                          size="sm"
                          className="bg-white/20 hover:bg-white/30 text-white border-0"
                          onClick={() => videoRef.current?.requestFullscreen()}
                        >
                          <Maximize className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Watch Progress */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-slate-900 dark:text-white">
                      تقدم المشاهدة
                    </h3>
                    <span className="text-sm text-slate-600 dark:text-slate-400">
                      {Math.round(watchProgress)}%
                    </span>
                  </div>
                  <Progress value={watchProgress} className="h-2" />
                  <p className="text-xs text-slate-500 dark:text-slate-500 mt-2">
                    يجب مشاهدة 80% على الأقل من المحاضرة لفتح الاختبار
                  </p>
                </CardContent>
              </Card>

              {/* Transcript */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileText className="w-5 h-5 text-blue-600" />
                      نص المحاضرة
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowTranscript(!showTranscript)}
                    >
                      {showTranscript ? "إخفاء" : "عرض"}
                    </Button>
                  </CardTitle>
                </CardHeader>
                {showTranscript && (
                  <CardContent>
                    <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 max-h-64 overflow-y-auto">
                      <pre className="text-sm text-slate-700 dark:text-slate-300 whitespace-pre-wrap font-cairo">
                        {lecture.transcript}
                      </pre>
                    </div>
                  </CardContent>
                )}
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quiz Card */}
              {lecture.hasQuiz && (
                <Card
                  className={`${
                    watchProgress >= 80
                      ? "border-green-200 dark:border-green-800"
                      : "border-slate-200 dark:border-slate-700"
                  }`}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="w-5 h-5 text-amber-600" />
                      اختبار المحاضرة
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {watchProgress >= 80 ? (
                      <div className="space-y-4">
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          أحسنت! يمكنك الآن إجراء اختبار هذه المحاضرة
                        </p>
                        <Link href={`/quiz/${lecture.id}`}>
                          <Button className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white">
                            ابدأ الاختبار
                            <ChevronLeft className="w-4 h-4 mr-2" />
                          </Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          شاهد 80% من المحاضرة على الأقل لفتح الاختبار
                        </p>
                        <Button disabled className="w-full">
                          الاختبار مقفل
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Navigation */}
              <Card>
                <CardHeader>
                  <CardTitle>التنقل</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {Number.parseInt(lectureId) > 1 && (
                    <Link href={`/lecture/${Number.parseInt(lectureId) - 1}`}>
                      <Button
                        variant="outline"
                        className="w-full justify-between bg-transparent"
                      >
                        <ChevronRight className="w-4 h-4" />
                        المحاضرة السابقة
                        <span></span>
                      </Button>
                    </Link>
                  )}

                  {Number.parseInt(lectureId) < 8 && (
                    <Link href={`/lecture/${Number.parseInt(lectureId) + 1}`}>
                      <Button
                        variant="outline"
                        className="w-full justify-between bg-transparent"
                      >
                        <span></span>
                        المحاضرة التالية
                        <ChevronLeft className="w-4 h-4" />
                      </Button>
                    </Link>
                  )}
                </CardContent>
              </Card>

              {/* Course Progress */}
              <Card>
                <CardHeader>
                  <CardTitle>تقدم الكورس</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>المحاضرات المكتملة</span>
                      <span>5 من 8</span>
                    </div>
                    <Progress value={62.5} />
                    <p className="text-xs text-slate-500 dark:text-slate-500">
                      أكمل جميع المحاضرات لفتح الاختبار النهائي
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
