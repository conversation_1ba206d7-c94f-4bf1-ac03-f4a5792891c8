"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import {
  Download,
  ArrowRight,
  Award,
  Calendar,
  CheckCircle,
  Star,
  Printer,
  Share2,
  Lock,
  MapPin,
  Navigation,
  Phone,
  Clock,
  Building,
  ExternalLink,
  FileText,
  Sparkles,
  Shield,
  QrCode,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import QRCode from "qrcode";

// Mock certificate data with dynamic user information
const mockCertificate = {
  studentName: "أبانوب نشأت صبحي",
  studentNameEn: "Abanoub Nashaat",
  courseName: "كورس الإعداد للزواج",
  courseNameEn: "Premarital Counseling Course",
  completionDate: "2024-01-20",
  completionDateEn: "January 20, 2024",
  certificateId: "CERT-2024-001234",
  grade: "ممتاز",
  gradeEn: "Excellent",
  gradePercentage: 95,
  issueDate: "2024-01-21",
  issueDateEn: "January 21, 2024",
  isAvailable: true,
  examScore: 95,
  courseDuration: "8 محاضرات",
  courseDurationEn: "8 Lectures",
  instructorName: "نيافة الأنبا نوفير",
  instructorNameEn: "His Grace Bishop Nofir",
  validationCode: "VALID-2024-001234",
};

// Metropolitan headquarters information
const metropolitanInfo = {
  name: "مطرانية شبين القناطر",
  nameEn: "Diocese of Shibin El Qanater",
  address: "شارع الكنيسة، شبين القناطر، محافظة القليوبية، مصر",
  addressEn: "Church Street, Shibin El Qanater, Qalyubia Governorate, Egypt",
  phone: "+20 13 123 4567",
  workingHours: "الأحد - الخميس: 9:00 ص - 5:00 م",
  workingHoursEn: "Sunday - Thursday: 9:00 AM - 5:00 PM",
  coordinates: {
    lat: 30.1626,
    lng: 31.3219,
  },
  mapUrl: "https://maps.google.com/?q=30.1626,31.3219",
};

export default function CertificatePage() {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showFullCertificate, setShowFullCertificate] = useState(false);
  const certificateRef = useRef<HTMLDivElement>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>("");

  // Generate QR code for certificate verification
  useEffect(() => {
    const generateQRCode = async () => {
      try {
        const verificationUrl = `${window.location.origin}/verify-certificate/${mockCertificate.certificateId}`;
        const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
          width: 200,
          margin: 2,
          color: {
            dark: "#1e293b",
            light: "#ffffff",
          },
        });
        setQrCodeUrl(qrCodeDataUrl);
      } catch (error) {
        console.error("Error generating QR code:", error);
      }
    };

    generateQRCode();
  }, []);

  const handleDownloadPDF = async () => {
    setIsDownloading(true);
    try {
      // Simulate PDF generation and download
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // In a real app, this would generate and download the actual PDF
      const link = document.createElement("a");
      link.href = "#"; // This would be the actual PDF URL
      link.download = `certificate-${mockCertificate.certificateId}.pdf`;
      // link.click()

      console.log("Certificate PDF downloaded successfully");
    } catch (error) {
      console.error("Error downloading certificate:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const handlePrint = () => {
    if (certificateRef.current) {
      const printContent = certificateRef.current.innerHTML;
      const originalContent = document.body.innerHTML;
      document.body.innerHTML = printContent;
      window.print();
      document.body.innerHTML = originalContent;
      window.location.reload();
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "شهادة إتمام كورس الإعداد للزواج",
          text: `تم إتمام كورس الإعداد للزواج بنجاح - ${mockCertificate.studentName}`,
          url: window.location.href,
        });
      } catch (error) {
        console.log("Error sharing:", error);
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(window.location.href);
      alert("تم نسخ الرابط إلى الحافظة");
    }
  };

  const openMap = () => {
    window.open(metropolitanInfo.mapUrl, "_blank");
  };

  if (!mockCertificate.isAvailable) {
    return (
      <div
        className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
        dir="rtl"
      >
        {/* Header */}
        <header className="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link
                  href="/dashboard"
                  className="flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                >
                  <ArrowRight className="w-5 h-5" />
                  العودة للوحة التحكم
                </Link>
              </div>
              <div className="flex items-center gap-4">
                <div className="relative w-10 h-10 rounded-xl overflow-hidden">
                  <Image
                    src="/diocese-logo.png"
                    alt="شعار مطرانية شبين القناطر"
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-slate-900 dark:text-white">
                    مشورة
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <Card className="p-12 border-2 border-dashed border-slate-300 dark:border-slate-600">
              <CardContent className="space-y-6">
                <div className="w-20 h-20 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto">
                  <Lock className="w-10 h-10 text-slate-400" />
                </div>
                <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
                  الشهادة غير متاحة
                </h1>
                <p className="text-slate-600 dark:text-slate-400 text-lg leading-relaxed">
                  يجب إكمال جميع المحاضرات واجتياز الاختبار النهائي بنجاح للحصول
                  على الشهادة
                </p>
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    المتطلبات للحصول على الشهادة:
                  </h3>
                  <ul className="text-blue-800 dark:text-blue-200 space-y-1 text-sm">
                    <li>• إكمال جميع المحاضرات الـ 8</li>
                    <li>• اجتياز جميع اختبارات المحاضرات</li>
                    <li>• النجاح في الاختبار النهائي بدرجة 80% أو أكثر</li>
                  </ul>
                </div>
                <Link href="/dashboard">
                  <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    العودة للوحة التحكم
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
      dir="rtl"
    >
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800 print:hidden">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors group"
              >
                <ArrowRight className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
                العودة للوحة التحكم
              </Link>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative w-10 h-10 rounded-xl overflow-hidden">
                <Image
                  src="/diocese-logo.png"
                  alt="شعار مطرانية شبين القناطر"
                  fill
                  className="object-cover"
                />
              </div>
              <div>
                <h1 className="text-lg font-bold text-slate-900 dark:text-white">
                  مشورة
                </h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Page Header */}
          <div className="text-center print:hidden">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center animate-pulse">
                <Award className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold text-slate-900 dark:text-white">
                  تهانينا!
                </h1>
                <p className="text-xl text-slate-600 dark:text-slate-400">
                  لقد أتممت الكورس بنجاح
                </p>
              </div>
            </div>
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 border border-green-200 dark:border-green-800 mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <CheckCircle className="w-6 h-6 text-green-600" />
                <h2 className="text-2xl font-bold text-green-800 dark:text-green-200">
                  شهادة إتمام الكورس
                </h2>
              </div>
              <p className="text-green-700 dark:text-green-300 text-lg">
                يمكنك الآن تحميل شهادتك الرقمية والتوجه لمقر المطرانية لاستلام
                النسخة المطبوعة والمعتمدة
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center print:hidden">
            <Button
              onClick={handleDownloadPDF}
              disabled={isDownloading}
              className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-8 py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group"
            >
              {isDownloading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin ml-2" />
                  جاري التحميل...
                </>
              ) : (
                <>
                  <Download className="w-5 h-5 ml-2 group-hover:animate-bounce" />
                  تحميل الشهادة الرقمية
                </>
              )}
            </Button>

            <Button
              onClick={handlePrint}
              variant="outline"
              className="border-2 border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800 hover:border-slate-400 dark:hover:border-slate-500 px-8 py-4 text-lg rounded-xl bg-transparent transition-all duration-300 transform hover:scale-105 group"
            >
              <Printer className="w-5 h-5 ml-2 group-hover:animate-pulse" />
              طباعة
            </Button>

            <Button
              onClick={handleShare}
              variant="outline"
              className="border-2 border-blue-300 dark:border-blue-600 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-400 dark:hover:border-blue-500 px-8 py-4 text-lg rounded-xl bg-transparent transition-all duration-300 transform hover:scale-105 group"
            >
              <Share2 className="w-5 h-5 ml-2 group-hover:rotate-12 transition-transform" />
              مشاركة
            </Button>

            <Button
              onClick={() => setShowFullCertificate(!showFullCertificate)}
              variant="outline"
              className="border-2 border-purple-300 dark:border-purple-600 text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:border-purple-400 dark:hover:border-purple-500 px-8 py-4 text-lg rounded-xl bg-transparent transition-all duration-300 transform hover:scale-105"
            >
              <FileText className="w-5 h-5 ml-2" />
              {showFullCertificate ? "إخفاء التفاصيل" : "عرض التفاصيل"}
            </Button>
          </div>

          {/* Certificate Preview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Digital Certificate */}
            <div className="space-y-6">
              <Card className="shadow-2xl border-0 bg-white dark:bg-slate-900 overflow-hidden">
                <div className="relative" ref={certificateRef}>
                  {/* Decorative Background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 dark:from-amber-900/20 dark:via-orange-900/20 dark:to-yellow-900/20"></div>
                  <div className="absolute top-0 left-0 w-full h-full opacity-10">
                    <div className="absolute top-8 right-8 w-32 h-32 border-4 border-amber-300 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-8 left-8 w-24 h-24 border-4 border-blue-300 rounded-full animate-pulse delay-1000"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border-2 border-indigo-200 rounded-full animate-spin-slow"></div>
                  </div>

                  <CardContent className="relative z-10 p-8 text-center space-y-6">
                    {/* Header */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-center gap-4 mb-4">
                        <div className="relative w-12 h-12 rounded-xl overflow-hidden">
                          <Image
                            src="/diocese-logo.png"
                            alt="شعار مطرانية شبين القناطر"
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="text-center">
                          <h2 className="text-lg font-bold text-slate-900 dark:text-white">
                            {metropolitanInfo.name}
                          </h2>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {metropolitanInfo.nameEn}
                          </p>
                        </div>
                      </div>

                      <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto animate-glow">
                        <Award className="w-8 h-8 text-white" />
                      </div>

                      <div>
                        <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
                          شهادة إتمام
                        </h1>
                        <h2 className="text-xl font-semibold text-amber-600 dark:text-amber-400">
                          Certificate of Completion
                        </h2>
                      </div>
                    </div>

                    {/* Certificate Content */}
                    <div className="space-y-4 py-6">
                      <p className="text-slate-600 dark:text-slate-400">
                        نشهد بأن
                      </p>
                      <p className="text-xs text-slate-500 dark:text-slate-500">
                        This is to certify that
                      </p>

                      <div className="py-4">
                        <h3 className="text-3xl font-bold text-slate-900 dark:text-white mb-1 border-b-2 border-amber-300 pb-2 inline-block">
                          {mockCertificate.studentName}
                        </h3>
                        <p className="text-lg text-slate-600 dark:text-slate-400 mt-2">
                          {mockCertificate.studentNameEn}
                        </p>
                      </div>

                      <div className="space-y-1">
                        <p className="text-slate-600 dark:text-slate-400">
                          قد أتم بنجاح
                        </p>
                        <p className="text-xs text-slate-500 dark:text-slate-500">
                          has successfully completed
                        </p>
                      </div>

                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-4 mx-4">
                        <h4 className="text-2xl font-bold text-blue-900 dark:text-blue-100 mb-1">
                          {mockCertificate.courseName}
                        </h4>
                        <p className="text-blue-700 dark:text-blue-300 text-sm">
                          {mockCertificate.courseNameEn}
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-4 py-4 text-sm">
                        <div className="text-center">
                          <Calendar className="w-4 h-4 text-green-600 mx-auto mb-1" />
                          <p className="text-xs text-slate-500 dark:text-slate-500">
                            تاريخ الإتمام
                          </p>
                          <p className="font-semibold text-slate-900 dark:text-white">
                            {mockCertificate.completionDate}
                          </p>
                        </div>

                        <div className="text-center">
                          <Star className="w-4 h-4 text-amber-600 mx-auto mb-1" />
                          <p className="text-xs text-slate-500 dark:text-slate-500">
                            التقدير
                          </p>
                          <p className="font-semibold text-slate-900 dark:text-white">
                            {mockCertificate.grade}
                          </p>
                          <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 text-xs">
                            {mockCertificate.gradePercentage}%
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="border-t border-slate-200 dark:border-slate-700 pt-4 space-y-3">
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div className="text-center">
                          <div className="w-20 h-0.5 bg-slate-300 dark:bg-slate-600 mx-auto mb-1"></div>
                          <p className="font-semibold text-slate-900 dark:text-white">
                            {mockCertificate.instructorName}
                          </p>
                          <p className="text-slate-600 dark:text-slate-400">
                            مطران شبين القناطر
                          </p>
                        </div>

                        <div className="text-center">
                          <div className="w-20 h-0.5 bg-slate-300 dark:bg-slate-600 mx-auto mb-1"></div>
                          <p className="font-semibold text-slate-900 dark:text-white">
                            تاريخ الإصدار
                          </p>
                          <p className="text-slate-600 dark:text-slate-400">
                            {mockCertificate.issueDate}
                          </p>
                        </div>
                      </div>

                      <div className="text-center pt-2">
                        <div className="flex items-center justify-center gap-4 mb-4">
                          {qrCodeUrl && (
                            <div className="bg-white p-2 rounded-lg shadow-sm">
                              <img
                                src={qrCodeUrl || "/placeholder.svg"}
                                alt="QR Code للتحقق من الشهادة"
                                className="w-16 h-16"
                              />
                            </div>
                          )}
                          <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-1">
                              <QrCode className="w-4 h-4 text-slate-500" />
                              <span className="text-xs font-mono text-slate-500">
                                {mockCertificate.certificateId}
                              </span>
                            </div>
                            <p className="text-xs text-slate-500 dark:text-slate-500">
                              امسح الكود للتحقق من صحة الشهادة
                            </p>
                          </div>
                        </div>
                        <p className="text-xs text-slate-500 dark:text-slate-500">
                          هذه الشهادة صادرة رسمياً من مطرانية شبين القناطر
                          ومعتمدة لإتمام مراسم الزواج
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>

              {/* Certificate Details */}
              {showFullCertificate && (
                <Card className="border border-blue-200 dark:border-blue-800">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3 text-blue-900 dark:text-blue-100">
                      <Shield className="w-5 h-5" />
                      تفاصيل الشهادة
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="space-y-3">
                        <div>
                          <span className="text-slate-500 dark:text-slate-400">
                            رقم الشهادة:
                          </span>
                          <span className="block font-mono text-slate-900 dark:text-white">
                            {mockCertificate.certificateId}
                          </span>
                        </div>
                        <div>
                          <span className="text-slate-500 dark:text-slate-400">
                            كود التحقق:
                          </span>
                          <span className="block font-mono text-slate-900 dark:text-white">
                            {mockCertificate.validationCode}
                          </span>
                        </div>
                        <div>
                          <span className="text-slate-500 dark:text-slate-400">
                            درجة الاختبار النهائي:
                          </span>
                          <span className="block font-semibold text-green-600">
                            {mockCertificate.examScore}%
                          </span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <span className="text-slate-500 dark:text-slate-400">
                            مدة الكورس:
                          </span>
                          <span className="block text-slate-900 dark:text-white">
                            {mockCertificate.courseDuration}
                          </span>
                        </div>
                        <div>
                          <span className="text-slate-500 dark:text-slate-400">
                            المدرب:
                          </span>
                          <span className="block text-slate-900 dark:text-white">
                            {mockCertificate.instructorName}
                          </span>
                        </div>
                        <div>
                          <span className="text-slate-500 dark:text-slate-400">
                            حالة الشهادة:
                          </span>
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                            معتمدة وصالحة
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Metropolitan Office Information */}
            <div className="space-y-6">
              {/* Important Notice */}
              <Card className="border-2 border-amber-300 dark:border-amber-600 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-amber-800 dark:text-amber-200">
                    <Building className="w-6 h-6" />
                    إشعار مهم
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-white/50 dark:bg-slate-800/50 rounded-xl p-4 border border-amber-200 dark:border-amber-700">
                    <div className="flex items-start gap-3">
                      <Sparkles className="w-6 h-6 text-amber-600 flex-shrink-0 mt-1" />
                      <div>
                        <h3 className="font-bold text-amber-900 dark:text-amber-100 mb-2">
                          برجاء التوجه لمقر المطرانية
                        </h3>
                        <p className="text-amber-800 dark:text-amber-200 leading-relaxed">
                          لتسلم الشهادة نسخة مطبوعة ومختومة ومعتمدة رسمياً. هذه
                          النسخة مطلوبة لإتمام إجراءات الزواج في الكنيسة.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="text-center">
                    <Button
                      onClick={openMap}
                      className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group"
                    >
                      <Navigation className="w-5 h-5 ml-2 group-hover:rotate-12 transition-transform" />
                      اعرض الموقع على الخريطة
                      <ExternalLink className="w-4 h-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Office Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-blue-600" />
                    معلومات المطرانية
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-slate-900 dark:text-white mb-2">
                        العنوان
                      </h3>
                      <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                        {metropolitanInfo.address}
                      </p>
                      <p className="text-sm text-slate-500 dark:text-slate-500 mt-1">
                        {metropolitanInfo.addressEn}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <Phone className="w-5 h-5 text-blue-600" />
                        <div>
                          <p className="text-sm text-slate-500 dark:text-slate-400">
                            الهاتف
                          </p>
                          <p
                            className="font-semibold text-slate-900 dark:text-white"
                            dir="ltr"
                          >
                            {metropolitanInfo.phone}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <Clock className="w-5 h-5 text-green-600" />
                        <div>
                          <p className="text-sm text-slate-500 dark:text-slate-400">
                            مواعيد العمل
                          </p>
                          <p className="font-semibold text-slate-900 dark:text-white text-sm">
                            {metropolitanInfo.workingHours}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Embedded Map */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-slate-900 dark:text-white">
                      الموقع على الخريطة
                    </h3>
                    <div className="relative w-full h-64 rounded-xl overflow-hidden border border-slate-200 dark:border-slate-700">
                      <iframe
                        src={`https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3453.123456789!2d${metropolitanInfo.coordinates.lng}!3d${metropolitanInfo.coordinates.lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzDCsDA5JzQ1LjQiTiAzMcKwMTknMTkuMCJF!5e0!3m2!1sen!2seg!4v1234567890123`}
                        width="100%"
                        height="100%"
                        style={{ border: 0 }}
                        allowFullScreen
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                        className="rounded-xl"
                      ></iframe>
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none"></div>
                    </div>
                    <div className="flex gap-3">
                      <Button
                        onClick={openMap}
                        variant="outline"
                        className="flex-1 border-blue-300 dark:border-blue-600 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 bg-transparent group"
                      >
                        <Navigation className="w-4 h-4 ml-2 group-hover:rotate-12 transition-transform" />
                        فتح في خرائط جوجل
                      </Button>
                      <Button
                        onClick={() => {
                          navigator.clipboard.writeText(
                            metropolitanInfo.address
                          );
                          alert("تم نسخ العنوان");
                        }}
                        variant="outline"
                        className="border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800 bg-transparent"
                      >
                        نسخ العنوان
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Required Documents */}
              <Card className="border border-purple-200 dark:border-purple-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-purple-900 dark:text-purple-100">
                    <FileText className="w-5 h-5" />
                    المستندات المطلوبة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                      <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-3">
                        احضر معك عند زيارة المطرانية:
                      </h4>
                      <ul className="space-y-2 text-purple-800 dark:text-purple-200">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span>بطاقة الهوية الشخصية</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span>نسخة من الشهادة الرقمية (مطبوعة)</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span>إيصال دفع رسوم الكورس</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
