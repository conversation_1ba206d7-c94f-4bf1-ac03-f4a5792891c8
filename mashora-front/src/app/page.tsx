"use client";
import Link from "next/link";
import { Button } from "@/components/ui/button";
// Card components removed as they're no longer used
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { OptimizedImage } from "@/components/optimized-image";
import { ModernNavbar } from "@/components/modern-navbar";
import {
  Heart,
  Users,
  BookOpen,
  Award,
  Clock,
  CheckCircle,
  Star,
  MessageCircle,
  Phone,
  Mail,
  MapPin,
  ChevronDown,
  Play,
  Download,
  Share2,
  User,
  Globe,
  Home,
} from "lucide-react";

// SEO Component
function SEOHead() {
  return (
    <>
      {/* Primary Meta Tags */}
      <title>كورس الإعداد للزواج - أبرشية شبين القناطر | مشورة</title>
      <meta
        name="title"
        content="كورس الإعداد للزواج - أبرشية شبين القناطر | مشورة"
      />
      <meta
        name="description"
        content="استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته عن الزواج المسيحي. برنامج معتمد من أبرشية شبين القناطر."
      />
      <meta
        name="keywords"
        content="زواج مسيحي, إعداد للزواج, كورس زواج, مشورة زواج, أبرشية شبين القناطر, الكنيسة القبطية, تأهيل للزواج, حياة زوجية سعيدة"
      />
      <meta name="robots" content="index, follow" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="Arabic" />
      <meta name="author" content="أبرشية شبين القناطر" />
      <meta name="copyright" content="أبرشية شبين القناطر" />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://mashora.diocese-shibin.org/" />
      <meta
        property="og:title"
        content="كورس الإعداد للزواج - أبرشية شبين القناطر"
      />
      <meta
        property="og:description"
        content="استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته عن الزواج المسيحي"
      />
      <meta
        property="og:image"
        content="https://mashora.diocese-shibin.org/og-image.jpg"
      />
      <meta property="og:site_name" content="مشورة - أبرشية شبين القناطر" />
      <meta property="og:locale" content="ar_EG" />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta
        property="twitter:url"
        content="https://mashora.diocese-shibin.org/"
      />
      <meta
        property="twitter:title"
        content="كورس الإعداد للزواج - أبرشية شبين القناطر"
      />
      <meta
        property="twitter:description"
        content="استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته عن الزواج المسيحي"
      />
      <meta
        property="twitter:image"
        content="https://mashora.diocese-shibin.org/og-image.jpg"
      />

      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#f59e0b" />
      <meta name="msapplication-TileColor" content="#f59e0b" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="format-detection" content="telephone=no" />

      {/* Canonical and Alternate */}
      <link rel="canonical" href="https://mashora.diocese-shibin.org/" />
      <link
        rel="alternate"
        hrefLang="ar"
        href="https://mashora.diocese-shibin.org/"
      />
      <link
        rel="alternate"
        hrefLang="en"
        href="https://mashora.diocese-shibin.org/en"
      />

      {/* Preconnect for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link
        rel="preconnect"
        href="https://fonts.gstatic.com"
        crossOrigin="anonymous"
      />

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@graph": [
              {
                "@type": "Course",
                name: "كورس الإعداد للزواج",
                description:
                  "كورس شامل للإعداد للزواج المسيحي يغطي جميع جوانب الحياة الزوجية",
                provider: {
                  "@type": "Organization",
                  name: "أبرشية شبين القناطر",
                  url: "https://diocese-shibin.org",
                },
                courseMode: "online",
                educationalLevel: "Beginner",
                inLanguage: "ar",
                offers: {
                  "@type": "Offer",
                  price: "0",
                  priceCurrency: "EGP",
                  availability: "https://schema.org/InStock",
                },
                aggregateRating: {
                  "@type": "AggregateRating",
                  ratingValue: "4.9",
                  reviewCount: "150",
                },
              },
              {
                "@type": "Organization",
                name: "أبرشية شبين القناطر",
                url: "https://diocese-shibin.org",
                logo: "https://mashora.diocese-shibin.org/diocese-logo.png",
                contactPoint: {
                  "@type": "ContactPoint",
                  telephone: "+20-13-123-4567",
                  contactType: "customer service",
                  availableLanguage: ["Arabic", "English"],
                },
                address: {
                  "@type": "PostalAddress",
                  streetAddress: "شارع الكنيسة",
                  addressLocality: "شبين القناطر",
                  addressRegion: "القليوبية",
                  addressCountry: "EG",
                },
              },
              {
                "@type": "WebSite",
                url: "https://mashora.diocese-shibin.org",
                name: "مشورة - كورس الإعداد للزواج",
                potentialAction: {
                  "@type": "SearchAction",
                  target:
                    "https://mashora.diocese-shibin.org/search?q={search_term_string}",
                  "query-input": "required name=search_term_string",
                },
              },
              {
                "@type": "WebPage",
                url: "https://mashora.diocese-shibin.org",
                name: "الصفحة الرئيسية - كورس الإعداد للزواج",
                description:
                  "استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته",
                inLanguage: "ar",
                isPartOf: {
                  "@type": "WebSite",
                  url: "https://mashora.diocese-shibin.org",
                },
              },
            ],
          }),
        }}
      />
    </>
  );
}

export default function HomePage() {
  return (
    <>
      <SEOHead />
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50/80 via-yellow-50/70 to-rose-50/90 dark:from-slate-950 dark:via-slate-900/95 dark:via-slate-800/90 dark:to-slate-900/95">
        {/* Enhanced Modern Navbar */}
        <ModernNavbar
          variant="default"
          navItems={[
            {
              href: "/",
              label: "الرئيسية",
              icon: <Home className="w-4 h-4" />,
              isActive: true,
            },
            {
              href: "#about",
              label: "عن الكورس",
              icon: <BookOpen className="w-4 h-4" />,
              isActive: false,
            },
            {
              href: "#features",
              label: "المميزات",
              icon: <Award className="w-4 h-4" />,
              isActive: false,
            },
            {
              href: "#testimonials",
              label: "التقييمات",
              icon: <MessageCircle className="w-4 h-4" />,
              isActive: false,
            },
            {
              href: "#contact",
              label: "تواصل معنا",
              icon: <Mail className="w-4 h-4" />,
              isActive: false,
            },
          ]}
        />

        {/* Hero Section */}
        <section className="relative py-16 sm:py-20 lg:py-24 px-4 text-center">
          <div className="container mx-auto max-w-4xl">
            <div className="mb-12 space-y-6">
              <Badge
                variant="secondary"
                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-amber-100 to-orange-100 text-amber-900 dark:bg-gradient-to-r dark:from-amber-900/40 dark:to-orange-900/40 dark:text-amber-200 rounded-full border border-amber-200/80 dark:border-amber-700/60 shadow-sm"
              >
                <Award className="w-4 h-4" />
                كورس معتمد من الأبرشية
              </Badge>
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
                <span className="block sm:inline text-slate-900 dark:text-slate-50">
                  كورس الإعداد
                </span>{" "}
                <span className="block sm:inline bg-gradient-to-r from-amber-600 via-orange-500 to-red-500 dark:from-amber-400 dark:via-orange-400 dark:to-red-400 bg-clip-text text-transparent">
                  للزواج المسيحي
                </span>
              </h1>
              <p className="text-lg sm:text-xl lg:text-2xl text-slate-700 dark:text-slate-200 max-w-4xl mx-auto leading-relaxed">
                استعد لحياة زوجية سعيدة ومستقرة من خلال برنامج شامل يغطي جميع
                جوانب الزواج المسيحي بأسلوب عملي ومعاصر
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button
                asChild
                size="lg"
                className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 dark:from-amber-600 dark:to-orange-600 dark:hover:from-amber-500 dark:hover:to-orange-500 text-white px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-300 rounded-full border"
              >
                <Link href="/signup">
                  <Heart className="w-5 h-5 ml-2" />
                  ابدأ رحلتك الآن
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="px-8 py-3 text-lg bg-transparent border-2  rounded-full border-slate-300 hover:border-amber-400 dark:border-slate-600 dark:hover:border-amber-500 hover:bg-amber-50 dark:hover:bg-amber-900/20 transition-all duration-300"
              >
                <Link href="#course-details">
                  <BookOpen className="w-5 h-5 ml-2" />
                  تفاصيل الكورس
                </Link>
              </Button>
            </div>

            {/* Enhanced Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 lg:gap-8 max-w-4xl mx-auto">
              <div className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl p-6 border border-amber-200/70 dark:border-slate-600/70 hover:shadow-lg hover:shadow-amber-500/20 dark:hover:shadow-amber-400/15 transition-all duration-300 hover:bg-white dark:hover:bg-slate-800 hover:border-amber-300 dark:hover:border-amber-500/50">
                <div className="text-center">
                  <div className="text-4xl font-bold text-amber-700 dark:text-amber-300 mb-2">
                    500+
                  </div>
                  <div className="text-slate-700 dark:text-slate-200 font-medium">
                    زوج وزوجة
                  </div>
                  <div className="text-sm text-slate-600 dark:text-slate-300 mt-1">
                    أتموا الكورس بنجاح
                  </div>
                </div>
              </div>
              <div className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl p-6 border border-amber-200/70 dark:border-slate-600/70 hover:shadow-lg hover:shadow-amber-500/20 dark:hover:shadow-amber-400/15 transition-all duration-300 hover:bg-white dark:hover:bg-slate-800 hover:border-amber-300 dark:hover:border-amber-500/50">
                <div className="text-center">
                  <div className="text-4xl font-bold text-amber-700 dark:text-amber-300 mb-2">
                    12
                  </div>
                  <div className="text-slate-700 dark:text-slate-200 font-medium">
                    محاضرة شاملة
                  </div>
                  <div className="text-sm text-slate-600 dark:text-slate-300 mt-1">
                    تغطي جميع الجوانب
                  </div>
                </div>
              </div>
              <div className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl p-6 border border-amber-200/70 dark:border-slate-600/70 hover:shadow-lg hover:shadow-amber-500/20 dark:hover:shadow-amber-400/15 transition-all duration-300 hover:bg-white dark:hover:bg-slate-800 hover:border-amber-300 dark:hover:border-amber-500/50">
                <div className="text-center">
                  <div className="text-4xl font-bold text-amber-700 dark:text-amber-300 mb-2">
                    98%
                  </div>
                  <div className="text-slate-700 dark:text-slate-200 font-medium">
                    معدل الرضا
                  </div>
                  <div className="text-sm text-slate-600 dark:text-slate-300 mt-1">
                    من المشاركين
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Course Details Section */}
        <section id="course-details" className="py-16 sm:py-20 lg:py-24 px-4">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-slate-50">
                ماذا ستتعلم في هذا الكورس؟
              </h2>
              <p className="text-xl text-slate-700 dark:text-slate-200 max-w-3xl mx-auto">
                كورس شامل يغطي جميع جوانب الحياة الزوجية من منظور مسيحي أصيل
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
              {[
                {
                  icon: Heart,
                  title: "أسس الحب المسيحي",
                  description:
                    "فهم معنى الحب الحقيقي وكيفية بنائه وتنميته في الزواج",
                  color: "from-red-400 to-pink-500",
                },
                {
                  icon: Users,
                  title: "التواصل الفعال",
                  description:
                    "تعلم مهارات التواصل والحوار البناء مع شريك الحياة",
                  color: "from-blue-400 to-indigo-500",
                },
                {
                  icon: BookOpen,
                  title: "الأسس الكتابية",
                  description:
                    "دراسة ما يعلمه الكتاب المقدس عن الزواج والأسرة المقدسة",
                  color: "from-emerald-400 to-teal-500",
                },
                {
                  icon: Award,
                  title: "إدارة التحديات",
                  description:
                    "كيفية مواجهة التحديات وحل الخلافات بطريقة مسيحية بناءة",
                  color: "from-amber-400 to-orange-500",
                },
                {
                  icon: Clock,
                  title: "إدارة الوقت والأولويات",
                  description:
                    "تعلم تنظيم الوقت بين العمل والعائلة والخدمة بتوازن",
                  color: "from-purple-400 to-violet-500",
                },
                {
                  icon: CheckCircle,
                  title: "التخطيط للمستقبل",
                  description:
                    "إدارة الشؤون المالية ووضع خطط مستقبلية للعائلة المسيحية",
                  color: "from-cyan-400 to-blue-500",
                },
              ].map((item, index) => (
                <div
                  key={index}
                  className="group relative bg-white/95 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl p-6 border border-slate-200/70 dark:border-slate-600/70 hover:shadow-xl hover:shadow-amber-500/20 dark:hover:shadow-amber-400/15 hover:border-amber-300 dark:hover:border-amber-500/60 transition-all duration-300 hover:-translate-y-1 hover:bg-white dark:hover:bg-slate-800"
                >
                  <div className="mb-4">
                    <div
                      className={`inline-flex items-center justify-center w-14 h-14 rounded-2xl bg-gradient-to-br ${item.color} shadow-lg`}
                    >
                      <item.icon className="w-7 h-7 text-white" />
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-slate-50 mb-3 group-hover:text-amber-700 dark:group-hover:text-amber-300 transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-slate-700 dark:text-slate-200 leading-relaxed text-sm">
                    {item.description}
                  </p>
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-amber-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 sm:py-20 lg:py-24 px-4">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16 space-y-4">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-slate-50 leading-tight">
                لماذا تختار كورسنا؟
              </h2>
              <p className="text-lg sm:text-xl text-slate-700 dark:text-slate-200 max-w-3xl mx-auto leading-relaxed">
                مميزات فريدة ومبتكرة تجعل تجربة التعلم أكثر فعالية وعمقاً
                وإثراءً
              </p>
              <div className="w-20 h-1 bg-gradient-to-r from-amber-500 to-orange-500 mx-auto rounded-full"></div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                {[
                  {
                    icon: Play,
                    title: "محاضرات تفاعلية",
                    description:
                      "محاضرات مصورة عالية الجودة مع إمكانية التفاعل والأسئلة",
                  },
                  {
                    icon: Download,
                    title: "مواد قابلة للتحميل",
                    description:
                      "كتيبات ومراجع يمكن تحميلها والرجوع إليها في أي وقت",
                  },
                  {
                    icon: Award,
                    title: "شهادة معتمدة",
                    description:
                      "احصل على شهادة معتمدة من الأبرشية عند إتمام الكورس",
                  },
                  {
                    icon: MessageCircle,
                    title: "دعم مستمر",
                    description:
                      "فريق من المرشدين المتخصصين لمساعدتك طوال الرحلة",
                  },
                ].map((benefit, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <benefit.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-slate-900 dark:text-slate-50">
                        {benefit.title}
                      </h3>
                      <p className="text-slate-700 dark:text-slate-200 leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="relative">
                <div className="aspect-video bg-gradient-to-br from-amber-100/95 to-orange-100/95 dark:from-slate-700/95 dark:to-slate-600/95 rounded-2xl flex items-center justify-center border border-amber-200/80 dark:border-slate-500/70 shadow-lg backdrop-blur-sm hover:border-amber-300 dark:hover:border-slate-400 transition-colors duration-300">
                  <div className="text-center group cursor-pointer">
                    <div className="w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg hover:scale-110 transition-transform duration-300">
                      <Play className="w-10 h-10 text-white ml-1" />
                    </div>
                    <p className="text-slate-800 dark:text-slate-200 text-lg font-medium group-hover:text-amber-700 dark:group-hover:text-amber-300 transition-colors">
                      شاهد مقدمة الكورس
                    </p>
                    <p className="text-slate-600 dark:text-slate-300 text-sm mt-2">
                      مدة الفيديو: 5 دقائق
                    </p>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-16 sm:py-20 lg:py-24 px-4">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16 space-y-4">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-slate-50 leading-tight">
                ماذا يقول المشاركون؟
              </h2>
              <p className="text-lg sm:text-xl text-slate-700 dark:text-slate-200 max-w-3xl mx-auto leading-relaxed">
                شهادات حقيقية وملهمة من أزواج استفادوا من الكورس وغيروا حياتهم
              </p>
              <div className="w-20 h-1 bg-gradient-to-r from-amber-500 to-orange-500 mx-auto rounded-full"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  name: "باسم ومريم",
                  text: "كورس رائع ساعدنا كثيراً في فهم أسس الزواج المسيحي. المحاضرات واضحة والمحتوى عملي ومفيد.",
                  rating: 5,
                  duration: "متزوجين منذ عامين",
                },
                {
                  name: "مينا وسارة",
                  text: "تعلمنا مهارات التواصل والحوار البناء. برنامج متكامل غير نظرتنا للزواج بشكل إيجابي تماماً.",
                  rating: 5,
                  duration: "متزوجين منذ 3 أعوام",
                },
                {
                  name: "جورج ونانسي",
                  text: "محتوى غني وعميق، والشهادة المعتمدة كانت مهمة جداً. ننصح جميع المقبلين على الزواج بهذا البرنامج.",
                  rating: 5,
                  duration: "متزوجين منذ 5 أعوام",
                },
              ].map((testimonial, index) => (
                <div
                  key={index}
                  className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl p-6 border border-slate-200/70 dark:border-slate-600/70 hover:shadow-xl hover:shadow-amber-500/20 dark:hover:shadow-amber-400/15 transition-all duration-300 hover:-translate-y-1 hover:bg-white dark:hover:bg-slate-800 hover:border-amber-300 dark:hover:border-amber-500/50"
                >
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-3">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star
                          key={i}
                          className="w-5 h-5 fill-amber-400 text-amber-400"
                        />
                      ))}
                    </div>
                    <h3 className="text-lg font-bold text-slate-900 dark:text-slate-50">
                      {testimonial.name}
                    </h3>
                    <p className="text-sm text-amber-700 dark:text-amber-300 font-medium">
                      {testimonial.duration}
                    </p>
                  </div>
                  <blockquote className="text-slate-700 dark:text-slate-200 leading-relaxed italic border-r-4 border-amber-500 dark:border-amber-400 pr-4">
                    &ldquo;{testimonial.text}&rdquo;
                  </blockquote>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 sm:py-20 lg:py-24 px-4">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center mb-16 space-y-4">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-slate-50 leading-tight">
                الأسئلة الشائعة
              </h2>
              <p className="text-lg sm:text-xl text-slate-700 dark:text-slate-200 max-w-3xl mx-auto leading-relaxed">
                إجابات واضحة وشاملة على أهم الأسئلة حول الكورس ومحتواه
              </p>
              <div className="w-20 h-1 bg-gradient-to-r from-amber-500 to-orange-500 mx-auto rounded-full"></div>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "كم تستغرق مدة الكورس؟",
                  answer:
                    "الكورس يستغرق 6 أسابيع بمعدل محاضرتين أسبوعياً، مع إمكانية التعلم بالسرعة التي تناسبك.",
                },
                {
                  question: "هل أحتاج لحضور محاضرات مباشرة؟",
                  answer:
                    "لا، جميع المحاضرات مسجلة ويمكنك مشاهدتها في أي وقت يناسبك.",
                },
                {
                  question: "كيف أحصل على الشهادة؟",
                  answer:
                    "بعد إتمام جميع المحاضرات واجتياز الاختبار النهائي، ستحصل على شهادة معتمدة من الأبرشية.",
                },
              ].map((faq, index) => (
                <div
                  key={index}
                  className="bg-white/90 dark:bg-slate-800/85 backdrop-blur-md rounded-2xl p-6 border border-slate-200/60 dark:border-slate-700/60 hover:shadow-lg hover:shadow-amber-500/15 transition-all duration-300 hover:bg-white/95 dark:hover:bg-slate-800/90"
                >
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-slate-900 dark:text-slate-50 flex items-center gap-2">
                      <ChevronDown className="w-5 h-5 text-amber-600 dark:text-amber-400" />
                      {faq.question}
                    </h3>
                  </div>
                  <div>
                    <p className="text-slate-700 dark:text-slate-200 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 sm:py-20 lg:py-24 px-4">
          <div className="container mx-auto max-w-4xl text-center">
            <div className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-md rounded-3xl p-8 sm:p-12 border border-amber-200/70 dark:border-slate-600/70 shadow-2xl hover:bg-white dark:hover:bg-slate-800 hover:shadow-amber-500/10 dark:hover:shadow-amber-400/10 transition-all duration-300">
              <div className="text-center mb-10">
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6 text-slate-900 dark:text-slate-50 leading-tight">
                  ابدأ رحلتك نحو زواج سعيد
                </h2>
                <p className="text-lg sm:text-xl text-slate-700 dark:text-slate-200 mb-8 leading-relaxed max-w-3xl mx-auto">
                  انضم إلى مئات الأزواج الذين استفادوا من برنامجنا المتخصص
                  وبناوا حياة زوجية مقدسة ومستقرة وسعيدة
                </p>
                <div className="w-24 h-1 bg-gradient-to-r from-amber-500 to-orange-500 mx-auto rounded-full mb-8"></div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <Button
                  asChild
                  size="lg"
                  className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 dark:from-amber-600 dark:to-orange-600 dark:hover:from-amber-500 dark:hover:to-orange-500 text-white px-10 py-4 text-lg font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <Link href="/signup" className="flex items-center gap-3">
                    <Heart className="w-6 h-6" />
                    سجل الآن مجاناً
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="px-10 py-4 text-lg font-semibold border-2 border-amber-400 hover:border-amber-500 dark:border-amber-500 dark:hover:border-amber-400 hover:bg-amber-50 dark:hover:bg-amber-900/20 text-amber-700 dark:text-amber-200 rounded-2xl transition-all duration-300"
                >
                  <Link href="/signin" className="flex items-center gap-3">
                    <User className="w-6 h-6" />
                    لديك حساب؟ ادخل
                  </Link>
                </Button>
              </div>

              <div className="flex flex-wrap items-center justify-center gap-6 text-sm font-medium">
                <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                  <CheckCircle className="w-5 h-5" />
                  شهادة معتمدة
                </div>
                <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                  <CheckCircle className="w-5 h-5" />
                  دعم مستمر
                </div>
                <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                  <CheckCircle className="w-5 h-5" />
                  محتوى حصري
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-slate-900 dark:bg-slate-950 text-slate-100 py-16 px-4">
          <div className="container mx-auto max-w-6xl">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
              {/* Logo and Description */}
              <div className="lg:col-span-2">
                <div className="flex items-center gap-4 mb-6">
                  <OptimizedImage
                    src="/diocese-logo.png"
                    alt="شعار أبرشية شبين القناطر"
                    width={50}
                    height={50}
                    className="rounded-full"
                  />
                  <div>
                    <h3 className="text-2xl font-bold">كورس المشورة</h3>
                    <p className="text-slate-400">مطرانية شبين القناطر وتوابعها</p>
                  </div>
                </div>
                <p className="text-slate-400 leading-relaxed mb-6">
                  نساعدك في الاستعداد لحياة زوجية سعيدة ومستقرة من خلال كورس
                  شامل يغطي جميع جوانب الزواج المسيحي.
                </p>
                <div className="flex gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-slate-600 text-slate-400 hover:text-slate-200 hover:bg-slate-800 hover:border-slate-500 bg-transparent transition-all duration-300"
                  >
                    <Share2 className="w-4 h-4 ml-2" />
                    شارك
                  </Button>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="text-lg font-semibold mb-4">روابط سريعة</h4>
                <ul className="space-y-2">
                  <li>
                    <Link
                      href="/signup"
                      className="text-slate-400 hover:text-slate-200 transition-colors"
                    >
                      التسجيل
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/signin"
                      className="text-slate-400 hover:text-slate-200 transition-colors"
                    >
                      تسجيل الدخول
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="#course-details"
                      className="text-slate-400 hover:text-slate-200 transition-colors"
                    >
                      تفاصيل الكورس
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/certificate"
                      className="text-slate-400 hover:text-slate-200 transition-colors"
                    >
                      الشهادات
                    </Link>
                  </li>
                </ul>
              </div>

              {/* Contact Info */}
              <div>
                <h4 className="text-lg font-semibold mb-4">تواصل معنا</h4>
                <ul className="space-y-3">
                  <li className="flex items-center gap-3">
                    <Phone className="w-4 h-4 text-amber-400" />
                    <span className="text-slate-400">+20-13-123-4567</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-amber-400" />
                    <span className="text-slate-400">
                      <EMAIL>
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-amber-400" />
                    <span className="text-slate-400">
                      شبين القناطر، القليوبية
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Globe className="w-4 h-4 text-amber-400" />
                    <span className="text-slate-400">diocese-shibin.org</span>
                  </li>
                </ul>
              </div>
            </div>

            <Separator className="bg-slate-700 mb-8" />

            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-slate-500 text-sm">
                © 2025 مطرانية شبين القناطر. جميع الحقوق محفوظة.
              </p>
              <div className="flex items-center gap-4 text-sm text-slate-500">
                <Link
                  href="/privacy"
                  className="hover:text-slate-200 transition-colors"
                >
                  سياسة الخصوصية
                </Link>
                <Link
                  href="/terms"
                  className="hover:text-slate-200 transition-colors"
                >
                  شروط الاستخدام
                </Link>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
