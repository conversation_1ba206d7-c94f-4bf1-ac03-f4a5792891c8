"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ModernNavbar } from "@/components/modern-navbar";
import {
  Play,
  Lock,
  CheckCircle,
  Download,
  FileText,
  LogOut,
  Menu,
  X,
  Award,
  Clock,
  BookOpen,
  Star,
  ChevronLeft,
  Home,
  Settings,
  TrendingUp,
  Calendar,
  Users,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

// Mock user data - in real app this would come from API/database
const mockUser = {
  firstName: "أبانوب",
  lastName: "نشأت",
  email: "<EMAIL>",
  avatar: "/placeholder.svg?height=80&width=80",
  overallProgress: 75,
  currentLecture: 6,
  completedLectures: [1, 2, 3, 4, 5],
  hasPassedFinalExam: false,
};

// Mock lectures data
const mockLectures = [
  {
    id: 1,
    number: "٠١",
    title: "مقدمة عن سر الزيجة",
    titleEn: "Introduction to the Sacrament of Marriage",
    duration: "45 دقيقة",
    description: "فهم المعنى الروحي والكتابي لسر الزيجة في المسيحية",
  },
  {
    id: 2,
    number: "٠٢",
    title: "التواصل الفعال بين الزوجين",
    titleEn: "Effective Communication Between Spouses",
    duration: "50 دقيقة",
    description: "تعلم مهارات التواصل الصحي وحل النزاعات",
  },
  {
    id: 3,
    number: "٠٣",
    title: "إدارة الأموال في الحياة الزوجية",
    titleEn: "Financial Management in Married Life",
    duration: "40 دقيقة",
    description: "التخطيط المالي والميزانية للأسرة الجديدة",
  },
  {
    id: 4,
    number: "٠٤",
    title: "العلاقة الحميمة والاحترام المتبادل",
    titleEn: "Intimacy and Mutual Respect",
    duration: "55 دقيقة",
    description: "بناء علاقة صحية قائمة على الحب والاحترام",
  },
  {
    id: 5,
    number: "٠٥",
    title: "التعامل مع الأهل والعائلة",
    titleEn: "Dealing with In-Laws and Family",
    duration: "35 دقيقة",
    description: "إقامة حدود صحية مع الأهل والعائلة الممتدة",
  },
  {
    id: 6,
    number: "٠٦",
    title: "التخطيط للمستقبل والأطفال",
    titleEn: "Planning for the Future and Children",
    duration: "45 دقيقة",
    description: "التحضير لاستقبال الأطفال والتربية المسيحية",
  },
  {
    id: 7,
    number: "٠٧",
    title: "الصلاة والحياة الروحية المشتركة",
    titleEn: "Prayer and Shared Spiritual Life",
    duration: "30 دقيقة",
    description: "بناء حياة روحية قوية كزوجين مسيحيين",
  },
  {
    id: 8,
    number: "٠٨",
    title: "مراجعة شاملة واستعداد للاختبار",
    titleEn: "Comprehensive Review and Exam Preparation",
    duration: "60 دقيقة",
    description: "مراجعة جميع المواضيع والاستعداد للاختبار النهائي",
  },
];

export default function DashboardPage() {
  const [animatedProgress, setAnimatedProgress] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Animate progress bar on load
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(mockUser.overallProgress);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Determine lecture state
  const getLectureState = (lectureId: number) => {
    if (mockUser.completedLectures.includes(lectureId)) {
      return "completed";
    } else if (
      lectureId === 1 ||
      mockUser.completedLectures.includes(lectureId - 1)
    ) {
      return "unlocked";
    } else {
      return "locked";
    }
  };

  // Get smart CTA button content
  const getSmartCTA = () => {
    if (mockUser.overallProgress === 0) {
      return {
        text: "ابدأ الدورة",
        subtext: "Start Course",
        action: () => console.log("Start course"),
        icon: Play,
      };
    } else if (mockUser.overallProgress < 100) {
      return {
        text: "أكمل الدورة",
        subtext: "Continue Course",
        action: () => console.log("Continue course"),
        icon: Play,
      };
    } else if (!mockUser.hasPassedFinalExam) {
      return {
        text: "ابدأ الاختبار النهائي",
        subtext: "Start Final Exam",
        action: () => console.log("Start exam"),
        icon: FileText,
      };
    } else {
      return {
        text: "حمّل شهادتك",
        subtext: "Download Certificate",
        action: () => console.log("Download certificate"),
        icon: Download,
      };
    }
  };

  const smartCTA = getSmartCTA();
  const SmartCTAIcon = smartCTA.icon;

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo relative overflow-x-hidden"
      dir="rtl"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23e2e8f0%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E')] opacity-40 pointer-events-none" />
      {/* Modern Navbar */}
      <ModernNavbar
        variant="dashboard"
        showUserMenu={true}
        userInfo={{
          name: `${mockUser.firstName} ${mockUser.lastName}`,
          email: mockUser.email,
          avatar: mockUser.avatar,
        }}
        navItems={[
          {
            href: "/dashboard",
            label: "لوحة التحكم",
            icon: <Home className="w-4 h-4" />,
            isActive: true,
          },
          {
            href: "/profile",
            label: "الملف الشخصي",
            icon: <Settings className="w-4 h-4" />,
            isActive: false,
          },
        ]}
      />

      <div className="flex relative">
        {/* Mobile Sidebar Toggle */}
        <div className="md:hidden fixed top-4 right-4 z-50">
          <Button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            variant="outline"
            size="icon"
            className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-md border-slate-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            {isMobileMenuOpen ? (
              <X className="w-5 h-5" />
            ) : (
              <Menu className="w-5 h-5" />
            )}
          </Button>
        </div>

        {/* Mobile Sidebar Overlay */}
        {isMobileMenuOpen && (
          <div
            className="md:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}

        {/* Sidebar */}
        <aside
          className={`
          fixed top-0 right-0 h-screen z-30 bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-l border-slate-200/50 dark:border-slate-800/50 shadow-2xl transition-all duration-300 ease-in-out
          ${
            isMobileMenuOpen
              ? "translate-x-0"
              : "translate-x-full md:translate-x-0"
          }
          w-80 md:w-72
        `}
        >
          {/* Sidebar top spacer for navbar */}
          <div className="h-20"></div>
          <div className="p-6 space-y-6 overflow-y-auto h-[calc(100vh-5rem)]">
            {/* User Profile */}
            <div className="text-center relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-2xl"></div>
              <div className="relative p-6 backdrop-blur-sm">
                <div className="relative w-24 h-24 mx-auto mb-4">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full blur-lg opacity-30 animate-pulse"></div>
                  <img
                    src={mockUser.avatar || "/placeholder.svg"}
                    alt="صورة المستخدم"
                    className="relative w-full h-full rounded-full object-cover border-4 border-white/50 dark:border-slate-700/50 shadow-lg"
                  />
                  <div className="absolute -bottom-1 -left-1 w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-3 border-white dark:border-slate-900 shadow-lg"></div>
                </div>
                <h3 className="font-bold text-slate-900 dark:text-white text-xl mb-1">
                  {mockUser.firstName} {mockUser.lastName}
                </h3>
                <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">
                  {mockUser.email}
                </p>
                <div className="flex items-center justify-center gap-3 text-xs">
                  <div className="flex items-center gap-1 bg-blue-50 dark:bg-blue-900/20 px-3 py-1 rounded-full">
                    <TrendingUp className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                    <span className="text-blue-600 dark:text-blue-400 font-medium">
                      {mockUser.overallProgress}%
                    </span>
                  </div>
                  <div className="flex items-center gap-1 bg-green-50 dark:bg-green-900/20 px-3 py-1 rounded-full">
                    <CheckCircle className="w-3 h-3 text-green-600 dark:text-green-400" />
                    <span className="text-green-600 dark:text-green-400 font-medium">
                      {mockUser.completedLectures.length}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Links */}
            <nav className="space-y-3">
              <Link
                href="/dashboard"
                className="flex items-center gap-3 px-4 py-3 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 text-blue-600 dark:text-blue-400 font-semibold border border-blue-200/50 dark:border-blue-800/50 shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800/50 rounded-lg flex items-center justify-center">
                  <Home className="w-4 h-4" />
                </div>
                لوحة التحكم
              </Link>

              <Link
                href="/profile"
                className="flex items-center gap-3 px-4 py-3 rounded-xl text-slate-600 dark:text-slate-400 hover:bg-slate-50/80 dark:hover:bg-slate-800/80 backdrop-blur-sm transition-all duration-200 hover:shadow-sm"
              >
                <div className="w-8 h-8 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center justify-center group-hover:bg-slate-200 dark:group-hover:bg-slate-700 transition-colors">
                  <Settings className="w-4 h-4" />
                </div>
                تعديل الملف الشخصي
              </Link>

              <Link
                href="/receipt"
                className="flex items-center gap-3 px-4 py-3 rounded-xl text-slate-600 dark:text-slate-400 hover:bg-slate-50/80 dark:hover:bg-slate-800/80 backdrop-blur-sm transition-all duration-200 hover:shadow-sm"
              >
                <div className="w-8 h-8 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4" />
                </div>
                تحميل إيصال الدفع
              </Link>

              <Link
                href="/certificate"
                className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                  mockUser.hasPassedFinalExam
                    ? "text-amber-600 dark:text-amber-400 hover:bg-amber-50/80 dark:hover:bg-amber-900/20 backdrop-blur-sm hover:shadow-sm"
                    : "text-slate-400 dark:text-slate-600 cursor-not-allowed opacity-50"
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                    mockUser.hasPassedFinalExam
                      ? "bg-amber-100 dark:bg-amber-900/30"
                      : "bg-slate-100 dark:bg-slate-800"
                  }`}
                >
                  <Award className="w-4 h-4" />
                </div>
                تحميل الشهادة
              </Link>

              <div className="border-t border-slate-200/50 dark:border-slate-700/50 pt-3 mt-6">
                <button className="flex items-center gap-3 px-4 py-3 rounded-xl text-red-600 dark:text-red-400 hover:bg-red-50/80 dark:hover:bg-red-900/20 backdrop-blur-sm transition-all duration-200 w-full text-right hover:shadow-sm">
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                    <LogOut className="w-4 h-4" />
                  </div>
                  تسجيل الخروج
                </button>
              </div>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 md:mr-72 p-4 md:p-6 lg:p-8 relative z-10 min-h-screen">
          <div className="max-w-4xl mx-auto space-y-6 md:space-y-8">
            {/* Welcome Section */}
            <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-600 rounded-2xl md:rounded-3xl p-6 md:p-8 text-white relative overflow-hidden shadow-xl">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/90 to-indigo-600/90"></div>
              {/* Animated Background Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full animate-pulse"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-white/5 rounded-full animate-pulse delay-300"></div>
              <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/5 rounded-full animate-pulse delay-700"></div>
              <div className="relative z-10">
                <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
                  <div className="mb-4 md:mb-0">
                    <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2">
                      أهلاً بك مرة أخرى، {mockUser.firstName}
                    </h2>
                    <p className="text-blue-100 text-base md:text-lg">
                      Welcome back, {mockUser.firstName}
                    </p>
                  </div>
                  <div className="hidden sm:block">
                    <div className="w-12 h-12 md:w-16 md:h-16 bg-white/20 rounded-full flex items-center justify-center">
                      <BookOpen className="w-6 h-6 md:w-8 md:h-8 text-white" />
                    </div>
                  </div>
                </div>

                {/* Overall Progress */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-white font-semibold">
                      التقدم الإجمالي
                    </span>
                    <span className="text-white font-bold">
                      {mockUser.overallProgress}% مكتمل
                    </span>
                  </div>
                  <div className="relative">
                    <Progress
                      value={animatedProgress}
                      className="h-4 bg-white/20 border border-white/10 shadow-inner"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse rounded-full"></div>
                  </div>
                  <p className="text-blue-100 text-sm">
                    {mockUser.completedLectures.length} من {mockLectures.length}{" "}
                    محاضرات مكتملة
                  </p>
                </div>
              </div>
            </div>

            {/* Smart CTA Button */}
            <Card className="border-0 shadow-2xl bg-gradient-to-r from-amber-50/80 to-orange-50/80 dark:from-amber-900/20 dark:to-orange-900/20 backdrop-blur-sm hover:shadow-3xl transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-8 text-center">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <SmartCTAIcon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                    {smartCTA.text}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400">
                    {smartCTA.subtext}
                  </p>
                </div>
                <Button
                  onClick={smartCTA.action}
                  size="lg"
                  className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-10 py-5 text-lg rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-amber-400/50 relative overflow-hidden group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                  <div className="relative flex items-center gap-2">
                    {smartCTA.text}
                    <ChevronLeft className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                  </div>
                </Button>
              </CardContent>
            </Card>

            {/* Lectures List */}
            <div className="space-y-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
                    <BookOpen className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
                      محاضرات الكورس
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Course Lectures
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Badge
                    variant="secondary"
                    className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full font-semibold border border-blue-200 dark:border-blue-800"
                  >
                    {mockLectures.length} محاضرات
                  </Badge>
                  <Badge className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-full font-semibold border border-green-200 dark:border-green-800">
                    {mockUser.completedLectures.length} مكتملة
                  </Badge>
                </div>
              </div>

              <div className="grid gap-4 md:gap-6">
                {mockLectures.map((lecture) => {
                  const state = getLectureState(lecture.id);

                  return (
                    <Card
                      key={lecture.id}
                      className={`transition-all duration-500 hover:shadow-2xl group relative overflow-hidden ${
                        state === "locked"
                          ? "opacity-60 bg-slate-50/80 dark:bg-slate-800/50 backdrop-blur-sm"
                          : state === "completed"
                          ? "bg-gradient-to-r from-green-50/80 to-emerald-50/80 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200/50 dark:border-green-800/50 backdrop-blur-sm shadow-lg hover:shadow-green-500/10"
                          : "bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:shadow-xl hover:shadow-blue-500/10 transform hover:-translate-y-2 border border-slate-200/50 dark:border-slate-700/50"
                      }`}
                    >
                      <CardContent className="p-4 md:p-6 lg:p-8 relative">
                        {/* Decorative corner */}
                        {state === "completed" && (
                          <div className="absolute top-0 right-0 w-0 h-0 border-l-[60px] border-l-transparent border-t-[60px] border-t-green-500/20"></div>
                        )}
                        {state !== "locked" && state !== "completed" && (
                          <div className="absolute top-0 right-0 w-0 h-0 border-l-[60px] border-l-transparent border-t-[60px] border-t-blue-500/20"></div>
                        )}
                        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                          {/* Lecture Number & Status Icon */}
                          <div className="flex-shrink-0 mx-auto sm:mx-0">
                            <div
                              className={`w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center text-white font-bold text-lg md:text-xl shadow-lg transition-all duration-300 ${
                                state === "locked"
                                  ? "bg-gradient-to-r from-slate-400 to-slate-500 dark:from-slate-600 dark:to-slate-700"
                                  : state === "completed"
                                  ? "bg-gradient-to-r from-green-500 to-emerald-500 shadow-green-500/30 group-hover:shadow-green-500/50 group-hover:scale-110"
                                  : "bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-500 shadow-blue-500/30 group-hover:shadow-blue-500/50 group-hover:scale-110"
                              }`}
                            >
                              {state === "locked" ? (
                                <Lock className="w-6 h-6" />
                              ) : state === "completed" ? (
                                <CheckCircle className="w-6 h-6" />
                              ) : (
                                <span>{lecture.number}</span>
                              )}
                            </div>
                          </div>

                          {/* Lecture Info */}
                          <div className="flex-1 min-w-0 text-center sm:text-right">
                            <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-4">
                              <div className="flex-1">
                                <h4
                                  className={`font-bold text-lg md:text-xl mb-2 transition-colors ${
                                    state === "locked"
                                      ? "text-slate-500 dark:text-slate-400"
                                      : "text-slate-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400"
                                  }`}
                                >
                                  المحاضرة {lecture.number}: {lecture.title}
                                </h4>
                                <p
                                  className={`text-sm mb-2 ${
                                    state === "locked"
                                      ? "text-slate-400 dark:text-slate-500"
                                      : "text-slate-600 dark:text-slate-400"
                                  }`}
                                >
                                  {lecture.titleEn}
                                </p>
                                <p
                                  className={`text-sm mb-3 ${
                                    state === "locked"
                                      ? "text-slate-400 dark:text-slate-500"
                                      : "text-slate-600 dark:text-slate-400"
                                  }`}
                                >
                                  {lecture.description}
                                </p>
                                <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2 md:gap-4">
                                  <Badge
                                    variant="outline"
                                    className="text-xs px-3 py-1 rounded-full bg-slate-50/80 dark:bg-slate-800/80 backdrop-blur-sm"
                                  >
                                    <Clock className="w-3 h-3 ml-1" />
                                    {lecture.duration}
                                  </Badge>
                                  {state === "completed" && (
                                    <Badge className="bg-green-100/80 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs px-3 py-1 rounded-full border border-green-200 dark:border-green-800 backdrop-blur-sm">
                                      <Star className="w-3 h-3 ml-1 animate-spin" />
                                      مكتملة
                                    </Badge>
                                  )}
                                  {state !== "locked" &&
                                    state !== "completed" && (
                                      <Badge className="bg-blue-100/80 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs px-3 py-1 rounded-full border border-blue-200 dark:border-blue-800 backdrop-blur-sm">
                                        <Play className="w-3 h-3 ml-1" />
                                        متاحة
                                      </Badge>
                                    )}
                                </div>
                              </div>

                              {/* Action Button */}
                              <div className="flex-shrink-0 w-full sm:w-auto mt-4 sm:mt-0">
                                {state === "locked" ? (
                                  <Button
                                    disabled
                                    variant="ghost"
                                    className="text-slate-400 bg-slate-100/50 dark:bg-slate-800/50 rounded-xl px-6 py-3 cursor-not-allowed backdrop-blur-sm"
                                  >
                                    <Lock className="w-4 h-4 ml-2" />
                                    مقفلة
                                  </Button>
                                ) : state === "completed" ? (
                                  <Button
                                    variant="outline"
                                    className="text-green-600 border-green-300/50 hover:bg-green-50/80 dark:hover:bg-green-900/20 bg-transparent backdrop-blur-sm rounded-xl px-6 py-3 shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-1 group"
                                  >
                                    <Play className="w-4 h-4 ml-2 group-hover:scale-110 transition-transform" />
                                    أعد المشاهدة
                                  </Button>
                                ) : (
                                  <Button className="bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-500 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-600 text-white rounded-xl px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:scale-105 relative overflow-hidden group">
                                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                                    <div className="relative flex items-center">
                                      <Play className="w-4 h-4 ml-2 group-hover:scale-110 transition-transform" />
                                      ابدأ المحاضرة
                                    </div>
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>

            {/* Final Exam Card */}
            <Card
              className={`border-2 transition-all duration-500 hover:shadow-2xl relative overflow-hidden backdrop-blur-sm ${
                mockUser.overallProgress === 100
                  ? "border-amber-300/50 bg-gradient-to-r from-amber-50/80 to-orange-50/80 dark:from-amber-900/20 dark:to-orange-900/20 shadow-xl hover:shadow-amber-500/20 hover:-translate-y-2"
                  : "border-slate-200/50 dark:border-slate-700/50 bg-slate-50/80 dark:bg-slate-800/50 hover:shadow-slate-500/10"
              }`}
            >
              {/* Decorative Elements */}
              {mockUser.overallProgress === 100 && (
                <>
                  <div className="absolute -top-2 -right-2 w-16 h-16 bg-amber-400/20 rounded-full animate-pulse"></div>
                  <div className="absolute -bottom-4 -left-4 w-24 h-24 bg-orange-400/10 rounded-full animate-pulse delay-300"></div>
                </>
              )}
              <CardContent className="p-10 text-center relative z-10">
                <div className="mb-8">
                  <div
                    className={`w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl transition-all duration-300 hover:scale-110 ${
                      mockUser.overallProgress === 100
                        ? "bg-gradient-to-r from-amber-500 via-amber-600 to-orange-500 shadow-amber-500/30 animate-pulse"
                        : "bg-gradient-to-r from-slate-300 to-slate-400 dark:from-slate-600 dark:to-slate-700 shadow-slate-500/20"
                    }`}
                  >
                    <FileText className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-slate-900 dark:text-white mb-3">
                    الاختبار النهائي
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400 text-lg">
                    Final Exam
                  </p>
                </div>

                {mockUser.overallProgress === 100 ? (
                  <div>
                    <p className="text-slate-600 dark:text-slate-400 mb-6">
                      تهانينا! لقد أكملت جميع المحاضرات. حان الوقت لإجراء
                      الاختبار النهائي.
                    </p>
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-amber-500 via-amber-600 to-orange-500 hover:from-amber-600 hover:via-amber-700 hover:to-orange-600 text-white px-12 py-6 text-xl rounded-2xl shadow-2xl hover:shadow-amber-500/30 transition-all duration-300 transform hover:scale-105 relative overflow-hidden group"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                      <div className="relative flex items-center gap-3">
                        <FileText className="w-6 h-6 group-hover:rotate-12 transition-transform" />
                        ابدأ الاختبار النهائي
                        <ChevronLeft className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </Button>
                  </div>
                ) : (
                  <div>
                    <p className="text-slate-500 dark:text-slate-400 mb-6">
                      أكمل جميع المحاضرات أولاً لفتح الاختبار النهائي
                    </p>
                    <Button
                      disabled
                      size="lg"
                      variant="outline"
                      className="px-10 py-6 text-lg rounded-xl bg-slate-100/50 dark:bg-slate-800/50 backdrop-blur-sm border-slate-300/50 dark:border-slate-600/50 cursor-not-allowed"
                    >
                      <Lock className="w-5 h-5 mr-3 animate-pulse" />
                      الاختبار مقفل
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
