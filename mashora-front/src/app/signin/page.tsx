"use client";

import type React from "react";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ModernNavbar } from "@/components/modern-navbar";
import {
  Eye,
  EyeOff,
  Mail,
  Phone,
  Lock,
  ArrowLeft,
  Heart,
  Sparkles,
  AlertCircle,
  CheckCircle,
  Loader2,
  Shield,
  User,
  Zap,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";

// Validation schema
const signinSchema = z.object({
  identifier: z.string().min(1, {
    message: "البريد الإلكتروني أو رقم الهاتف مطلوب",
  }),
  password: z.string().min(1, {
    message: "كلمة المرور مطلوبة",
  }),
});

type SigninFormData = z.infer<typeof signinSchema>;

export default function SigninPage() {
  const [formData, setFormData] = useState<SigninFormData>({
    identifier: "",
    password: "",
  });
  const [errors, setErrors] = useState<
    Partial<Record<keyof SigninFormData, string>>
  >({});
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [loadingProgress, setLoadingProgress] = useState(0);

  const handleInputChange = (field: keyof SigninFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    try {
      signinSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Partial<Record<keyof SigninFormData, string>> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as keyof SigninFormData] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setSubmitStatus("idle");
    setLoadingProgress(0);

    // Simulate loading progress
    const progressInterval = setInterval(() => {
      setLoadingProgress((prev) => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 100);

    try {
      // Simulate API call
      const response = await fetch("/auth/signin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      clearInterval(progressInterval);
      setLoadingProgress(100);

      if (response.ok) {
        setSubmitStatus("success");
        // Redirect to dashboard or course page
        setTimeout(() => {
          window.location.href = "/dashboard";
        }, 1500);
      } else {
        setSubmitStatus("error");
      }
    } catch (error) {
      clearInterval(progressInterval);
      setLoadingProgress(0);
      setSubmitStatus("error");
    } finally {
      setTimeout(() => {
        setIsLoading(false);
        setLoadingProgress(0);
      }, 500);
    }
  };

  const isEmailOrPhone = (identifier: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^(\+20|0020|20)?1[0125][0-9]{8}$/;
    return emailRegex.test(identifier) || phoneRegex.test(identifier);
  };

  return (
    <div
      className="min-h-screen bg-background text-foreground font-cairo overflow-x-hidden transition-colors duration-300"
      dir="rtl"
    >
      {/* Enhanced Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-amber-50/30 to-orange-50/50 dark:from-slate-950 dark:via-slate-900/80 dark:to-slate-950 transition-all duration-700" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(251,191,36,0.15),transparent_40%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(251,191,36,0.08),transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.1),transparent_40%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.05),transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_90%_10%,rgba(236,72,153,0.08),transparent_40%)] dark:bg-[radial-gradient(circle_at_90%_10%,rgba(236,72,153,0.04),transparent_40%)]" />
      </div>

      {/* Modern Navbar */}
      <ModernNavbar variant="minimal" />

      {/* Main Content */}
      <main className="relative z-10 py-8 lg:py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-lg mx-auto">
            {/* Enhanced Page Header */}
            <div className="text-center mb-10 animate-fade-in-up">
              <div className="inline-flex items-center gap-2 mb-6">
                <Badge className="bg-gradient-to-r from-amber-500/20 via-orange-500/20 to-red-500/20 text-amber-700 dark:text-amber-300 hover:from-amber-500/30 hover:via-orange-500/30 hover:to-red-500/30 text-sm px-6 py-3 border border-amber-500/30 backdrop-blur-sm transition-all duration-300 hover:scale-105">
                  <Heart className="w-4 h-4 ml-2 animate-pulse" />
                  تسجيل الدخول • Sign In
                </Badge>
              </div>

              <div className="space-y-4">
                <h1 className="text-5xl lg:text-6xl font-black text-foreground">
                  <span className="bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent animate-gradient">
                    أهلاً بعودتك
                  </span>
                </h1>
                <h2 className="text-2xl font-semibold text-slate-600 dark:text-slate-400">
                  Welcome Back
                </h2>
                <p className="text-slate-500 dark:text-slate-400 text-lg max-w-md mx-auto leading-relaxed">
                  سجل دخولك لمتابعة كورس الإعداد للزواج
                </p>
              </div>
            </div>

            {/* Enhanced Signin Form */}
            <Card className="relative overflow-hidden border-0 shadow-2xl bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl animate-scale-in">
              {/* Decorative Elements */}
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-amber-500 via-orange-500 to-red-500" />

              <div className="absolute top-8 left-8 w-1 h-1 bg-orange-400 rounded-full animate-pulse" />

              <CardHeader className="text-center pb-6 pt-8">
                <div className="flex items-center justify-center mb-4">
                  <div className="p-3 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl shadow-lg">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                </div>
                <CardTitle className="text-3xl font-bold text-foreground">
                  تسجيل الدخول
                </CardTitle>
                <p className="text-slate-500 dark:text-slate-400 mt-2">
                  أدخل بياناتك للوصول لحسابك
                </p>
              </CardHeader>

              <CardContent className="space-y-8 px-8 pb-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Enhanced Identifier Field */}
                  <div className="space-y-3">
                    <Label
                      htmlFor="identifier"
                      className="text-foreground font-semibold flex items-center gap-2 text-lg"
                    >
                      {formData.identifier &&
                      isEmailOrPhone(formData.identifier) ? (
                        formData.identifier.includes("@") ? (
                          <Mail className="w-5 h-5 text-amber-500" />
                        ) : (
                          <Phone className="w-5 h-5 text-amber-500" />
                        )
                      ) : (
                        <User className="w-5 h-5 text-amber-500" />
                      )}
                      البريد الإلكتروني أو رقم الهاتف
                    </Label>
                    <div className="relative group">
                      <Input
                        id="identifier"
                        type="text"
                        placeholder="<EMAIL> | 01012345678"
                        value={formData.identifier}
                        onChange={(e) =>
                          handleInputChange("identifier", e.target.value)
                        }
                        disabled={isLoading}
                        className={`text-right text-lg py-4 px-6 border-2 transition-all duration-300 ${
                          errors.identifier
                            ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                            : isLoading
                            ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                            : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                        } rounded-xl shadow-sm focus:shadow-lg`}
                        dir="ltr"
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                      {isLoading && (
                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                          <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                        </div>
                      )}
                    </div>
                    {errors.identifier && (
                      <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                        <AlertCircle className="w-4 h-4 flex-shrink-0" />
                        {errors.identifier}
                      </div>
                    )}
                  </div>

                  {/* Enhanced Password Field */}
                  <div className="space-y-3">
                    <Label
                      htmlFor="password"
                      className="text-foreground font-semibold flex items-center gap-2 text-lg"
                    >
                      <Lock className="w-5 h-5 text-amber-500" />
                      كلمة المرور
                    </Label>
                    <div className="relative group">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="أدخل كلمة المرور"
                        value={formData.password}
                        onChange={(e) =>
                          handleInputChange("password", e.target.value)
                        }
                        disabled={isLoading}
                        className={`text-right text-lg py-4 px-6 pl-14 border-2 transition-all duration-300 ${
                          errors.password
                            ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                            : isLoading
                            ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                            : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                        } rounded-xl shadow-sm focus:shadow-lg`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isLoading}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 text-slate-400 hover:text-amber-500 transition-all duration-300 hover:bg-amber-50 dark:hover:bg-amber-900/20 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {showPassword ? (
                          <EyeOff className="w-5 h-5" />
                        ) : (
                          <Eye className="w-5 h-5" />
                        )}
                      </button>
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                    </div>
                    {errors.password && (
                      <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                        <AlertCircle className="w-4 h-4 flex-shrink-0" />
                        {errors.password}
                      </div>
                    )}
                  </div>

                  {/* Enhanced Forgot Password Link */}
                  <div className="text-left">
                    <Link
                      href="/forgot-password"
                      className={`inline-flex items-center gap-2 text-sm font-semibold transition-all duration-300 hover:gap-3 group ${
                        isLoading
                          ? "text-slate-400 cursor-not-allowed pointer-events-none"
                          : "text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300"
                      }`}
                    >
                      <Zap
                        className={`w-4 h-4 ${
                          !isLoading && "group-hover:animate-pulse"
                        }`}
                      />
                      نسيت كلمة المرور؟
                    </Link>
                  </div>

                  {/* Enhanced Submit Status */}
                  {submitStatus === "success" && (
                    <div className="flex items-center gap-3 text-green-700 dark:text-green-300 text-sm bg-green-50 dark:bg-green-900/20 p-4 rounded-xl border border-green-200 dark:border-green-800 animate-fade-in">
                      <CheckCircle className="w-5 h-5 flex-shrink-0" />
                      <div>
                        <div className="font-semibold">
                          تم تسجيل الدخول بنجاح!
                        </div>
                        <div className="text-green-600 dark:text-green-400">
                          جاري التوجيه...
                        </div>
                      </div>
                    </div>
                  )}

                  {submitStatus === "error" && (
                    <div className="flex items-center gap-3 text-red-700 dark:text-red-300 text-sm bg-red-50 dark:bg-red-900/20 p-4 rounded-xl border border-red-200 dark:border-red-800 animate-fade-in">
                      <AlertCircle className="w-5 h-5 flex-shrink-0" />
                      <div>
                        <div className="font-semibold">خطأ في البيانات</div>
                        <div className="text-red-600 dark:text-red-400">
                          تأكد من صحة البريد الإلكتروني وكلمة المرور
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Enhanced Submit Button */}
                  <div className="space-y-4">
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="w-full bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 hover:from-amber-600 hover:via-orange-600 hover:to-red-600 text-white text-xl font-bold py-6 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none relative overflow-hidden group"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
                      {isLoading ? (
                        <div className="flex items-center gap-3">
                          <Loader2 className="w-6 h-6 animate-spin" />
                          جاري تسجيل الدخول...
                        </div>
                      ) : (
                        <div className="flex items-center gap-3">
                          <span>تسجيل الدخول</span>
                          <ArrowLeft className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
                        </div>
                      )}
                    </Button>

                    {/* Loading Progress Bar */}
                    {isLoading && (
                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 rounded-full transition-all duration-300 ease-out"
                          style={{ width: `${loadingProgress}%` }}
                        />
                      </div>
                    )}
                  </div>
                </form>

                {/* Enhanced Divider */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t-2 border-slate-200 dark:border-slate-700"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-6 bg-white dark:bg-slate-900 text-slate-500 dark:text-slate-400 font-medium">
                      أو
                    </span>
                  </div>
                </div>

                {/* Enhanced Signup Link */}
                <div className="text-center space-y-4">
                  <p className="text-slate-600 dark:text-slate-400 text-lg">
                    ليس لديك حساب؟
                  </p>
                  <Link href="/signup">
                    <Button
                      variant="outline"
                      disabled={isLoading}
                      className={`w-full border-2 text-lg font-semibold py-6 rounded-2xl backdrop-blur-sm transition-all duration-300 transform group ${
                        isLoading
                          ? "border-slate-300 dark:border-slate-600 text-slate-400 dark:text-slate-500 bg-slate-100 dark:bg-slate-800/30 cursor-not-allowed"
                          : "border-slate-200 dark:border-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-50 dark:hover:bg-amber-900/20 hover:border-amber-400 dark:hover:border-amber-400 bg-white/50 dark:bg-slate-800/50 hover:scale-[1.02]"
                      }`}
                    >
                      <Sparkles
                        className={`w-6 h-6 ml-3 ${
                          !isLoading && "group-hover:animate-pulse"
                        }`}
                      />
                      إنشاء حساب جديد
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Back to Home */}
            <div className="text-center mt-10 animate-fade-in-up">
              <Link
                href="/"
                className={`inline-flex items-center gap-3 transition-all duration-300 group ${
                  isLoading
                    ? "text-slate-400 cursor-not-allowed pointer-events-none"
                    : "text-slate-500 dark:text-slate-400 hover:text-amber-500 dark:hover:text-amber-400 hover:gap-4"
                }`}
              >
                <ArrowLeft
                  className={`w-5 h-5 ${
                    !isLoading &&
                    "group-hover:translate-x-1 transition-transform duration-300"
                  }`}
                />
                <span className="font-medium">العودة للصفحة الرئيسية</span>
              </Link>
            </div>
          </div>
        </div>
      </main>

      {/* Enhanced Floating Elements */}
      <div className="absolute top-32 right-16 w-24 h-24 bg-gradient-to-br from-amber-400/20 to-orange-400/20 rounded-full animate-float blur-sm" />
      <div
        className="absolute bottom-32 left-16 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full animate-float blur-sm"
        style={{ animationDelay: "2s" }}
      />
      <div
        className="absolute top-1/2 right-1/4 w-16 h-16 bg-gradient-to-br from-pink-400/20 to-red-400/20 rounded-full animate-float blur-sm"
        style={{ animationDelay: "4s" }}
      />
    </div>
  );
}
