import { NextRequest, NextResponse } from "next/server";

// Mock admin user data
const MOCK_ADMIN_USER = {
  id: 1,
  username: "admin",
  name: "المدير العام",
  email: "<EMAIL>",
  role: "admin",
  permissions: [
    "view_users",
    "edit_users",
    "delete_users",
    "view_stats",
    "manage_certificates",
  ],
};

export async function GET(request: NextRequest) {
  try {
    const authorization = request.headers.get("authorization");

    if (!authorization) {
      return NextResponse.json(
        {
          success: false,
          message: "رمز التفويض مطلوب",
        },
        { status: 401 }
      );
    }

    const token = authorization.replace("Bearer ", "");

    // Simple mock token validation (starts with mock_admin_token_)
    if (token.startsWith("mock_admin_token_")) {
      return NextResponse.json({
        success: true,
        user: MOCK_ADMIN_USER,
        message: "رمز التفويض صحيح",
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: "رمز التفويض غير صحيح",
        },
        { status: 401 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: "حدث خطأ في الخادم",
      },
      { status: 500 }
    );
  }
}
