import { NextRequest, NextResponse } from "next/server";

// Mock admin credentials for testing
const ADMIN_CREDENTIALS = {
  username: "admin",
  password: "admin123",
};

// Mock admin user data
const MOCK_ADMIN_USER = {
  id: 1,
  username: "admin",
  name: "المدير العام",
  email: "<EMAIL>",
  role: "admin",
  permissions: [
    "view_users",
    "edit_users",
    "delete_users",
    "view_stats",
    "manage_certificates",
  ],
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password } = body;

    // Validate credentials
    if (
      username === ADMIN_CREDENTIALS.username &&
      password === ADMIN_CREDENTIALS.password
    ) {
      // Generate mock token (in production, use proper JWT)
      const token = `mock_admin_token_${Date.now()}_${Math.random()}`;

      return NextResponse.json({
        success: true,
        token,
        user: MOCK_ADMIN_USER,
        message: "تم تسجيل الدخول بنجاح",
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: "اسم المستخدم أو كلمة المرور غير صحيح",
        },
        { status: 401 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: "حدث خطأ في الخادم",
      },
      { status: 500 }
    );
  }
}
