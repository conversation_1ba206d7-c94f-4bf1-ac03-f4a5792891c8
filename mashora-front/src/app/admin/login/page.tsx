"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

import {
  Eye,
  EyeOff,
  Shield,
  Lock,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Loader2,
  User,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { z } from "zod";

// Validation schema
const adminLoginSchema = z.object({
  username: z.string().min(1, {
    message: "اسم المستخدم مطلوب",
  }),
  password: z.string().min(1, {
    message: "كلمة المرور مطلوبة",
  }),
});

type AdminLoginFormData = z.infer<typeof adminLoginSchema>;

export default function AdminLoginPage() {
  const [formData, setFormData] = useState<AdminLoginFormData>({
    username: "",
    password: "",
  });
  const [errors, setErrors] = useState<
    Partial<Record<keyof AdminLoginFormData, string>>
  >({});
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState("");

  const router = useRouter();

  const handleInputChange = (
    field: keyof AdminLoginFormData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
    if (submitStatus === "error") {
      setSubmitStatus("idle");
      setErrorMessage("");
    }
  };

  const validateForm = (): boolean => {
    try {
      adminLoginSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Partial<Record<keyof AdminLoginFormData, string>> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as keyof AdminLoginFormData] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setSubmitStatus("idle");

    try {
      // Simulate API call for admin authentication
      const response = await fetch("/api/admin/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        // Store admin token
        localStorage.setItem("adminToken", data.token);
        setSubmitStatus("success");

        // Redirect to admin dashboard
        setTimeout(() => {
          router.push("/admin");
        }, 1500);
      } else {
        const errorData = await response.json();
        setSubmitStatus("error");
        setErrorMessage(
          errorData.message || "خطأ في اسم المستخدم أو كلمة المرور"
        );
      }
    } catch (error) {
      setSubmitStatus("error");
      setErrorMessage("حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen bg-background text-foreground font-cairo overflow-x-hidden transition-colors duration-300"
      dir="rtl"
    >
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 transition-all duration-500" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(239,68,68,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(59,130,246,0.1),transparent_50%)]" />
      </div>

      {/* Header */}
      <header className="sticky top-0 z-50 glass-card border-b border-slate-800/50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/"
            className="flex items-center gap-4 animate-fade-in-up hover:opacity-80 transition-opacity"
          >
            <div className="relative w-14 h-14 rounded-xl overflow-hidden animate-glow">
              <Image
                src="/diocese-logo.png"
                alt="شعار مطرانية شبين القناطر"
                fill
                className="object-cover"
                priority
              />
            </div>
            <div>
              <h1 className="font-bold text-foreground text-lg">
                مطرانية شبين القناطر
              </h1>
              <p className="text-xs text-slate-400">
                Diocese of Shibin El Qanater
              </p>
            </div>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto">
            {/* Page Header */}
            <div className="text-center mb-8 animate-fade-in-up">
              <Badge className="mb-6 bg-gradient-to-r from-red-500/20 to-orange-500/20 text-red-600 dark:text-red-300 hover:from-red-500/30 hover:to-orange-500/30 text-sm px-6 py-3 border border-red-500/30">
                <Shield className="w-4 h-4 ml-2" />
                تسجيل دخول الإدارة • Admin Login
              </Badge>

              <h1 className="text-4xl font-black text-foreground mb-4">
                <span className="gradient-text">لوحة تحكم الإدارة</span>
              </h1>
              <h2 className="text-xl font-semibold text-slate-400 mb-2">
                Admin Dashboard
              </h2>
              <p className="text-slate-500">سجل دخولك للوصول إلى لوحة التحكم</p>
            </div>

            {/* Admin Login Form */}
            <Card className="glass-card border border-slate-700/50 shadow-2xl animate-scale-in">
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-2xl font-bold text-foreground flex items-center justify-center gap-3">
                  <Lock className="w-6 h-6 text-red-400" />
                  تسجيل دخول المدير
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Username Field */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="username"
                      className="text-foreground font-semibold flex items-center gap-2"
                    >
                      <User className="w-4 h-4 text-red-400" />
                      اسم المستخدم *
                    </Label>
                    <Input
                      id="username"
                      type="text"
                      placeholder="أدخل اسم المستخدم"
                      value={formData.username}
                      onChange={(e) =>
                        handleInputChange("username", e.target.value)
                      }
                      className={`text-right ${
                        errors.username
                          ? "border-red-500 focus:border-red-500"
                          : ""
                      }`}
                    />
                    {errors.username && (
                      <div className="flex items-center gap-2 text-red-500 text-sm">
                        <AlertCircle className="w-4 h-4" />
                        {errors.username}
                      </div>
                    )}
                  </div>

                  {/* Password Field */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="password"
                      className="text-foreground font-semibold flex items-center gap-2"
                    >
                      <Lock className="w-4 h-4 text-red-400" />
                      كلمة المرور *
                    </Label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="أدخل كلمة المرور"
                        value={formData.password}
                        onChange={(e) =>
                          handleInputChange("password", e.target.value)
                        }
                        className={`text-right pr-12 ${
                          errors.password
                            ? "border-red-500 focus:border-red-500"
                            : ""
                        }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-red-400 transition-colors"
                      >
                        {showPassword ? (
                          <EyeOff className="w-5 h-5" />
                        ) : (
                          <Eye className="w-5 h-5" />
                        )}
                      </button>
                    </div>
                    {errors.password && (
                      <div className="flex items-center gap-2 text-red-500 text-sm">
                        <AlertCircle className="w-4 h-4" />
                        {errors.password}
                      </div>
                    )}
                  </div>

                  {/* Submit Status */}
                  {submitStatus === "success" && (
                    <div className="flex items-center gap-2 text-green-600 text-sm bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                      <CheckCircle className="w-4 h-4" />
                      تم تسجيل الدخول بنجاح! جاري التوجيه...
                    </div>
                  )}

                  {submitStatus === "error" && (
                    <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                      <AlertCircle className="w-4 h-4" />
                      {errorMessage}
                    </div>
                  )}

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white text-lg py-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-5 h-5 ml-2 animate-spin" />
                        جاري تسجيل الدخول...
                      </>
                    ) : (
                      <>
                        دخول لوحة التحكم
                        <ArrowLeft className="w-5 h-5 mr-2" />
                      </>
                    )}
                  </Button>
                </form>

                {/* Security Notice */}
                <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
                  <div className="flex items-start gap-3">
                    <Shield className="w-5 h-5 text-amber-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-amber-800 dark:text-amber-300 mb-1">
                        تنبيه أمني
                      </h4>
                      <p className="text-sm text-amber-700 dark:text-amber-400">
                        هذه المنطقة مخصصة للمديرين فقط. جميع عمليات الدخول يتم
                        تسجيلها ومراقبتها.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Back to Home */}
            <div className="text-center mt-8 animate-fade-in-up">
              <Link
                href="/"
                className="inline-flex items-center gap-2 text-slate-400 hover:text-red-400 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                العودة للصفحة الرئيسية
              </Link>
            </div>
          </div>
        </div>
      </main>

      {/* Floating Elements */}
      <div className="absolute top-20 right-10 w-20 h-20 floating-element rounded-full animate-float" />
      <div
        className="absolute bottom-20 left-10 w-16 h-16 floating-element rounded-full animate-float"
        style={{ animationDelay: "2s" }}
      />
    </div>
  );
}
