"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Progress } from "@/components/ui/progress";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Users,
  BookOpen,
  Award,
  CreditCard,
  Search,
  Download,
  Plus,
  Edit,
  Trash2,
  Eye,
  QrCode,
  FileText,
  BarChart3,
  Settings,
  Upload,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  Camera,
  Scan,
  RefreshCw,
  Mail,
  Phone,
  GraduationCap,
  TrendingUp,
  DollarSign,
  Activity,
  Database,
  Bell,
  Home,
  LogOut,
  Save,
  MapPin,
  AlertTriangle,
  Shield,
  Key,
  UserCheck,
  Send,
  Copy,
  ExternalLink,
  FileVideo,
  FileImage,
  FileIcon as FilePdf,
  Loader2,
  SortAsc,
  SortDesc,
  UserPlus,
  Sparkles,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";

// Auth check hook
const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    // Check if user is authenticated admin
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem("adminToken");
        if (!token) {
          router.push("/admin/login");
          return;
        }

        // Simulate API call to verify admin token
        const response = await fetch("/api/admin/verify", {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (response.ok) {
          const userData = await response.json();
          setUser(userData.user);
          setIsAuthenticated(true);
        } else {
          localStorage.removeItem("adminToken");
          router.push("/admin/login");
        }
      } catch (error) {
        console.error("Auth check failed:", error);
        router.push("/admin/login");
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  return { isAuthenticated, isLoading, user };
};

// Mock admin data with expanded features
const mockAdminData = {
  stats: {
    totalUsers: 156,
    activeUsers: 142,
    completedCourses: 89,
    pendingPayments: 12,
    totalRevenue: 31200,
    certificatesIssued: 89,
    averageScore: 87.5,
    completionRate: 76.3,
    newUsersThisMonth: 23,
    averageCompletionTime: 14, // days
    topPerformingLecture: "مقدمة عن سر الزيجة",
    failureRate: 8.2,
  },
  users: [
    {
      id: 1,
      name: "أبانوب نشأت صبحي",
      nameEn: "Abanoub Nashaat",
      email: "<EMAIL>",
      phone: "+20 ************",
      registrationDate: "2024-01-15",
      progress: 100,
      paymentStatus: "paid",
      paymentAmount: 200,
      paymentDate: "2024-01-15",
      paymentMethod: "visa",
      finalExamScore: 95,
      finalExamAttempts: 1,
      certificateIssued: true,
      certificateId: "CERT-2024-001234",
      status: "completed",
      lastActivity: "2024-01-20",
      lecturesCompleted: 8,
      totalLectures: 8,
      quizScores: [92, 88, 95, 90, 87, 93, 89, 91],
      timeSpent: 720, // minutes
      location: "القاهرة",
      notes: "طالب متميز، أكمل الكورس في وقت قياسي",
    },
    {
      id: 2,
      name: "مريم يوسف إبراهيم",
      nameEn: "Mariam Youssef",
      email: "<EMAIL>",
      phone: "+20 ************",
      registrationDate: "2024-01-18",
      progress: 75,
      paymentStatus: "paid",
      paymentAmount: 200,
      paymentDate: "2024-01-18",
      paymentMethod: "mastercard",
      finalExamScore: null,
      finalExamAttempts: 0,
      certificateIssued: false,
      certificateId: null,
      status: "in_progress",
      lastActivity: "2024-01-22",
      lecturesCompleted: 6,
      totalLectures: 8,
      quizScores: [85, 90, 88, 92, 87, 89],
      timeSpent: 540,
      location: "الجيزة",
      notes: "تقدم جيد، تحتاج لإكمال المحاضرتين الأخيرتين",
    },
    {
      id: 3,
      name: "يوحنا مينا جرجس",
      nameEn: "Youhanna Mina",
      email: "<EMAIL>",
      phone: "+20 ************",
      registrationDate: "2024-01-20",
      progress: 25,
      paymentStatus: "pending",
      paymentAmount: 200,
      paymentDate: null,
      paymentMethod: null,
      finalExamScore: null,
      finalExamAttempts: 0,
      certificateIssued: false,
      certificateId: null,
      status: "payment_pending",
      lastActivity: "2024-01-21",
      lecturesCompleted: 2,
      totalLectures: 8,
      quizScores: [78, 82],
      timeSpent: 180,
      location: "الإسكندرية",
      notes: "في انتظار تأكيد الدفع",
    },
  ],
  lectures: [
    {
      id: 1,
      title: "مقدمة عن سر الزيجة",
      titleEn: "Introduction to the Sacrament of Marriage",
      description: "محاضرة تمهيدية تشرح أساسيات سر الزيجة في المسيحية",
      duration: 45,
      order: 1,
      status: "published",
      enrollments: 156,
      completions: 142,
      averageScore: 89.5,
      videoUrl: "/videos/lecture-1.mp4",
      thumbnailUrl: "/images/lecture-1-thumb.jpg",
      materials: [
        {
          type: "pdf",
          name: "ملخص المحاضرة",
          url: "/materials/lecture-1-summary.pdf",
        },
        {
          type: "ppt",
          name: "العرض التقديمي",
          url: "/materials/lecture-1-slides.ppt",
        },
      ],
      quiz: {
        id: 1,
        questions: [
          {
            id: 1,
            question: "ما هو تعريف سر الزيجة في المسيحية؟",
            options: [
              "عقد مدني بين رجل وامرأة",
              "سر مقدس يوحد بين رجل وامرأة أمام الله",
              "احتفال اجتماعي",
              "تقليد كنسي",
            ],
            correctAnswer: 1,
            difficulty: "easy",
          },
          {
            id: 2,
            question: "كم عدد أسرار الكنيسة؟",
            options: ["خمسة", "ستة", "سبعة", "ثمانية"],
            correctAnswer: 2,
            difficulty: "medium",
          },
        ],
      },
      createdAt: "2024-01-01",
      updatedAt: "2024-01-15",
    },
    {
      id: 2,
      title: "التواصل الفعال بين الزوجين",
      titleEn: "Effective Communication Between Spouses",
      description: "كيفية بناء تواصل صحي وفعال في الحياة الزوجية",
      duration: 50,
      order: 2,
      status: "published",
      enrollments: 142,
      completions: 128,
      averageScore: 87.2,
      videoUrl: "/videos/lecture-2.mp4",
      thumbnailUrl: "/images/lecture-2-thumb.jpg",
      materials: [
        {
          type: "pdf",
          name: "دليل التواصل الفعال",
          url: "/materials/lecture-2-guide.pdf",
        },
      ],
      quiz: {
        id: 2,
        questions: [
          {
            id: 3,
            question: "ما هي أهم مهارات التواصل الفعال؟",
            options: [
              "الاستماع الجيد",
              "التحدث بوضوح",
              "التعبير عن المشاعر",
              "جميع ما سبق",
            ],
            correctAnswer: 3,
            difficulty: "easy",
          },
        ],
      },
      createdAt: "2024-01-02",
      updatedAt: "2024-01-16",
    },
  ],
  certificates: [
    {
      id: "CERT-2024-001234",
      studentId: 1,
      studentName: "أبانوب نشأت صبحي",
      studentEmail: "<EMAIL>",
      issueDate: "2024-01-20",
      score: 95,
      status: "valid",
      verificationCount: 3,
      qrCode:
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      downloadCount: 2,
      lastVerified: "2024-01-22",
    },
    {
      id: "CERT-2024-001235",
      studentId: 4,
      studentName: "صموئيل نشأت صبحي",
      studentEmail: "<EMAIL>",
      issueDate: "2024-01-19",
      score: 88,
      status: "valid",
      verificationCount: 1,
      qrCode:
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      downloadCount: 1,
      lastVerified: "2024-01-21",
    },
  ],
  finalExam: {
    id: 1,
    title: "الاختبار النهائي - كورس الإعداد للزواج",
    description: "اختبار شامل يغطي جميع محاور الكورس",
    duration: 120, // minutes
    passingScore: 80,
    maxAttempts: 3,
    questions: [
      {
        id: 1,
        question: "ما هي الأسس الروحية للزواج المسيحي؟",
        options: [
          "المحبة والاحترام المتبادل",
          "الصلاة والعبادة المشتركة",
          "الالتزام مدى الحياة",
          "جميع ما سبق",
        ],
        correctAnswer: 3,
        difficulty: "medium",
        category: "أساسيات الزواج",
      },
      {
        id: 2,
        question: "كيف يمكن حل الخلافات الزوجية بطريقة مسيحية؟",
        options: [
          "تجنب الحديث عن المشاكل",
          "الحوار الهادئ والمغفرة",
          "طلب المساعدة من الأهل فقط",
          "الانفصال المؤقت",
        ],
        correctAnswer: 1,
        difficulty: "hard",
        category: "حل النزاعات",
      },
    ],
    isActive: true,
    createdAt: "2024-01-01",
    updatedAt: "2024-01-15",
  },
  payments: [
    {
      id: 1,
      userId: 1,
      amount: 200,
      currency: "EGP",
      status: "completed",
      method: "visa",
      transactionId: "TXN-2024-001234",
      date: "2024-01-15",
      receiptUrl: "/receipts/receipt-001234.pdf",
    },
    {
      id: 2,
      userId: 2,
      amount: 200,
      currency: "EGP",
      status: "completed",
      method: "mastercard",
      transactionId: "TXN-2024-001235",
      date: "2024-01-18",
      receiptUrl: "/receipts/receipt-001235.pdf",
    },
    {
      id: 3,
      userId: 3,
      amount: 200,
      currency: "EGP",
      status: "pending",
      method: null,
      transactionId: null,
      date: null,
      receiptUrl: null,
    },
  ],
};

export default function AdminDashboard() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [tabLoading, setTabLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [qrScannerOpen, setQrScannerOpen] = useState(false);
  const [certificateVerification, setCertificateVerification] = useState("");
  const [verificationResult, setVerificationResult] = useState<any>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [editingLecture, setEditingLecture] = useState<any>(null);
  const [newLecture, setNewLecture] = useState<any>(null);
  const [editingQuestion, setEditingQuestion] = useState<any>(null);
  const [newQuestion, setNewQuestion] = useState<any>(null);
  const [sortField, setSortField] = useState("registrationDate");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [bulkAction, setBulkAction] = useState("");
  const [emailTemplate, setEmailTemplate] = useState("");
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [showManualCertificateDialog, setShowManualCertificateDialog] =
    useState(false);
  const [showEditUserDialog, setShowEditUserDialog] = useState(false);
  const [showAddUserDialog, setShowAddUserDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);
  const [newUser, setNewUser] = useState<any>({
    name: "",
    nameEn: "",
    email: "",
    phone: "",
    location: "",
    notes: "",
  });
  const [manualCertificate, setManualCertificate] = useState({
    studentName: "",
    studentEmail: "",
    score: 80,
    issueDate: new Date().toISOString().split("T")[0],
  });
  const [systemSettings, setSystemSettings] = useState({
    coursePrice: 200,
    passingScore: 80,
    examDuration: 120,
    maxAttempts: 3,
    autoApprovePayments: false,
    emailNotifications: true,
    smsNotifications: false,
  });
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const router = useRouter();

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-slate-600 dark:text-slate-400">
            جاري التحقق من الصلاحيات...
          </p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Handle tab change with loading
  const handleTabChange = async (value: string) => {
    if (value === activeTab) return;

    setTabLoading(true);
    // Simulate data loading
    await new Promise((resolve) => setTimeout(resolve, 800));
    setActiveTab(value);
    setTabLoading(false);
  };

  // Filter and sort users
  const filteredUsers = mockAdminData.users
    .filter((user) => {
      const matchesSearch =
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.phone.includes(searchTerm);
      const matchesStatus =
        filterStatus === "all" || user.status === filterStatus;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      const aValue = a[sortField as keyof typeof a];
      const bValue = b[sortField as keyof typeof b];
      const direction = sortDirection === "asc" ? 1 : -1;
      return (aValue || "") > (bValue || "") ? direction : -direction;
    });

  const handleLogout = () => {
    localStorage.removeItem("adminToken");
    router.push("/admin/login");
  };

  const handleCertificateVerification = async () => {
    if (!certificateVerification.trim()) return;

    setIsScanning(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    const certificate = mockAdminData.certificates.find(
      (cert) => cert.id === certificateVerification.trim()
    );

    setVerificationResult(certificate || { error: "Certificate not found" });
    setIsScanning(false);
  };

  // User Actions
  const handleUserAction = async (action: string, userId: number) => {
    setActionLoading(`${action}-${userId}`);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      switch (action) {
        case "view":
          const user = mockAdminData.users.find((u) => u.id === userId);
          setSelectedUser(user);
          break;
        case "edit":
          const editUser = mockAdminData.users.find((u) => u.id === userId);
          setEditingUser(editUser);
          setShowEditUserDialog(true);
          break;
        case "send_email":
          alert(`تم إرسال رسالة بريدية للمستخدم ${userId}`);
          break;
        case "download_receipt":
          alert(`تم تحميل الإيصال للمستخدم ${userId}`);
          break;
        case "view_certificate":
          alert(`عرض شهادة المستخدم ${userId}`);
          break;
        case "suspend":
          alert(`تم تعليق المستخدم ${userId}`);
          break;
        case "delete":
          alert(`تم حذف المستخدم ${userId}`);
          break;
        case "generate_certificate":
          alert(`تم إنشاء شهادة للمستخدم ${userId}`);
          break;
      }
    } catch (error) {
      console.error("Error performing action:", error);
      alert("حدث خطأ أثناء تنفيذ العملية");
    } finally {
      setActionLoading(null);
    }
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedUsers.length === 0) return;

    setActionLoading("bulk");

    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));

      switch (bulkAction) {
        case "send_email":
          setShowEmailDialog(true);
          break;
        case "export_data":
          alert(`تم تصدير بيانات ${selectedUsers.length} مستخدم`);
          break;
        case "suspend_users":
          alert(`تم تعليق ${selectedUsers.length} مستخدم`);
          break;
        case "generate_certificates":
          alert(`تم إنشاء شهادات لـ ${selectedUsers.length} مستخدم`);
          break;
      }
    } catch (error) {
      console.error("Error performing bulk action:", error);
      alert("حدث خطأ أثناء تنفيذ العملية الجماعية");
    } finally {
      setActionLoading(null);
    }
  };

  const handleSendBulkEmail = async () => {
    setActionLoading("send_email");

    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      alert(`تم إرسال الرسالة لـ ${selectedUsers.length} مستخدم`);
      setShowEmailDialog(false);
      setEmailTemplate("");
      setSelectedUsers([]);
      setBulkAction("");
    } catch (error) {
      console.error("Error sending bulk email:", error);
      alert("حدث خطأ أثناء إرسال الرسائل");
    } finally {
      setActionLoading(null);
    }
  };

  const handleSaveSettings = async () => {
    setActionLoading("save_settings");

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      alert("تم حفظ الإعدادات بنجاح");
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("حدث خطأ أثناء حفظ الإعدادات");
    } finally {
      setActionLoading(null);
    }
  };

  const handleAddUser = async () => {
    setActionLoading("add_user");

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      alert("تم إضافة المستخدم بنجاح");
      setShowAddUserDialog(false);
      setNewUser({
        name: "",
        nameEn: "",
        email: "",
        phone: "",
        location: "",
        notes: "",
      });
    } catch (error) {
      console.error("Error adding user:", error);
      alert("حدث خطأ أثناء إضافة المستخدم");
    } finally {
      setActionLoading(null);
    }
  };

  const handleEditUser = async () => {
    setActionLoading("edit_user");

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      alert("تم تحديث بيانات المستخدم بنجاح");
      setShowEditUserDialog(false);
      setEditingUser(null);
    } catch (error) {
      console.error("Error editing user:", error);
      alert("حدث خطأ أثناء تحديث البيانات");
    } finally {
      setActionLoading(null);
    }
  };

  const handleCreateManualCertificate = async () => {
    setActionLoading("create_certificate");

    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      const certificateId = `CERT-2024-${String(Date.now()).slice(-6)}`;
      alert(`تم إنشاء الشهادة بنجاح\nرقم الشهادة: ${certificateId}`);
      setShowManualCertificateDialog(false);
      setManualCertificate({
        studentName: "",
        studentEmail: "",
        score: 80,
        issueDate: new Date().toISOString().split("T")[0],
      });
    } catch (error) {
      console.error("Error creating certificate:", error);
      alert("حدث خطأ أثناء إنشاء الشهادة");
    } finally {
      setActionLoading(null);
    }
  };

  // Payment Actions
  const handlePaymentAction = async (action: string, paymentId: number) => {
    setActionLoading(`payment-${action}-${paymentId}`);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));

      switch (action) {
        case "download_receipt":
          alert(`تم تحميل الإيصال للدفعة ${paymentId}`);
          break;
        case "view_details":
          alert(`عرض تفاصيل الدفعة ${paymentId}`);
          break;
        case "approve_payment":
          alert(`تم الموافقة على الدفعة ${paymentId}`);
          break;
        case "refund":
          alert(`تم استرداد الدفعة ${paymentId}`);
          break;
      }
    } catch (error) {
      console.error("Error performing payment action:", error);
      alert("حدث خطأ أثناء تنفيذ العملية");
    } finally {
      setActionLoading(null);
    }
  };

  // Certificate Actions
  const handleCertificateAction = async (
    action: string,
    certificateId: string
  ) => {
    setActionLoading(`cert-${action}-${certificateId}`);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));

      switch (action) {
        case "view":
          alert(`عرض الشهادة ${certificateId}`);
          break;
        case "download":
          alert(`تم تحميل الشهادة ${certificateId}`);
          break;
        case "share":
          alert(`تم نسخ رابط الشهادة ${certificateId}`);
          break;
        case "copy_link":
          navigator.clipboard.writeText(
            `${window.location.origin}/verify-certificate/${certificateId}`
          );
          alert("تم نسخ الرابط");
          break;
        case "send_email":
          alert(`تم إرسال الشهادة ${certificateId} بالبريد الإلكتروني`);
          break;
        case "revoke":
          alert(`تم إلغاء الشهادة ${certificateId}`);
          break;
      }
    } catch (error) {
      console.error("Error performing certificate action:", error);
      alert("حدث خطأ أثناء تنفيذ العملية");
    } finally {
      setActionLoading(null);
    }
  };

  // Lecture Actions
  const handleLectureAction = async (action: string, lectureId: number) => {
    setActionLoading(`lecture-${action}-${lectureId}`);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));

      switch (action) {
        case "edit":
          const lecture = mockAdminData.lectures.find(
            (l) => l.id === lectureId
          );
          setEditingLecture(lecture);
          break;
        case "preview":
          alert(`معاينة المحاضرة ${lectureId}`);
          break;
        case "upload_video":
          alert(`رفع فيديو للمحاضرة ${lectureId}`);
          break;
        case "view_stats":
          alert(`إحصائيات المحاضرة ${lectureId}`);
          break;
        case "delete":
          alert(`تم حذف المحاضرة ${lectureId}`);
          break;
      }
    } catch (error) {
      console.error("Error performing lecture action:", error);
      alert("حدث خطأ أثناء تنفيذ العملية");
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: {
        label: "مكتمل",
        color:
          "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      },
      in_progress: {
        label: "قيد التقدم",
        color:
          "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      },
      payment_pending: {
        label: "في انتظار الدفع",
        color:
          "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",
      },
      suspended: {
        label: "معلق",
        color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] ||
      statusConfig.in_progress;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getPaymentStatusBadge = (status: string) => {
    const statusConfig = {
      paid: {
        label: "مدفوع",
        color:
          "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
        icon: CheckCircle,
      },
      completed: {
        label: "مكتمل",
        color:
          "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
        icon: CheckCircle,
      },
      pending: {
        label: "معلق",
        color:
          "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",
        icon: Clock,
      },
      failed: {
        label: "فشل",
        color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
        icon: XCircle,
      },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 ml-1" />
        {config.label}
      </Badge>
    );
  };

  const getDifficultyBadge = (difficulty: string) => {
    const difficultyConfig = {
      easy: {
        label: "سهل",
        color:
          "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      },
      medium: {
        label: "متوسط",
        color:
          "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",
      },
      hard: {
        label: "صعب",
        color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      },
    };

    const config =
      difficultyConfig[difficulty as keyof typeof difficultyConfig] ||
      difficultyConfig.medium;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
      dir="rtl"
    >
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo and Title */}
            <div className="flex items-center gap-4">
              <div className="relative w-12 h-12 rounded-xl overflow-hidden">
                <Image
                  src="/diocese-logo.png"
                  alt="شعار مطرانية شبين القناطر"
                  fill
                  className="object-cover"
                  priority
                />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900 dark:text-white">
                  لوحة تحكم الإدارة
                </h1>
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  مرحباً {user?.name || "المدير"}
                </p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center gap-6">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                <Home className="w-4 h-4" />
                الصفحة الرئيسية
              </Link>
              <Button
                variant="ghost"
                size="sm"
                className="text-slate-600 dark:text-slate-400"
              >
                <Bell className="w-4 h-4 ml-2" />
                الإشعارات
                <Badge className="mr-2 bg-red-500 text-white text-xs px-1.5 py-0.5">
                  3
                </Badge>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="text-red-600 dark:text-red-400"
                onClick={handleLogout}
              >
                <LogOut className="w-4 h-4 ml-2" />
                تسجيل الخروج
              </Button>
            </nav>

            {/* Mobile Menu */}
            <div className="md:hidden flex items-center gap-2">
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Enhanced Stats Overview */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">إجمالي المستخدمين</p>
                    <p className="text-3xl font-bold">
                      {mockAdminData.stats.totalUsers}
                    </p>
                    <p className="text-blue-100 text-xs">
                      +{mockAdminData.stats.newUsersThisMonth} هذا الشهر
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-blue-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">الكورسات المكتملة</p>
                    <p className="text-3xl font-bold">
                      {mockAdminData.stats.completedCourses}
                    </p>
                    <p className="text-green-100 text-xs">
                      {mockAdminData.stats.completionRate}% معدل الإتمام
                    </p>
                  </div>
                  <GraduationCap className="w-8 h-8 text-green-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-amber-500 to-amber-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-amber-100 text-sm">إجمالي الإيرادات</p>
                    <p className="text-3xl font-bold">
                      {mockAdminData.stats.totalRevenue.toLocaleString()} ج.م
                    </p>
                    <p className="text-amber-100 text-xs">
                      {mockAdminData.stats.pendingPayments} دفعة معلقة
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-amber-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">الشهادات الصادرة</p>
                    <p className="text-3xl font-bold">
                      {mockAdminData.stats.certificatesIssued}
                    </p>
                    <p className="text-purple-100 text-xs">
                      متوسط الدرجات {mockAdminData.stats.averageScore}%
                    </p>
                  </div>
                  <Award className="w-8 h-8 text-purple-200" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs
            value={activeTab}
            onValueChange={handleTabChange}
            className="space-y-6"
          >
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-6 h-auto p-1 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
              <TabsTrigger
                value="overview"
                className="flex items-center gap-2 py-3 px-4 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                <BarChart3 className="w-4 h-4" />
                <span className="hidden sm:inline">نظرة عامة</span>
              </TabsTrigger>
              <TabsTrigger
                value="users"
                className="flex items-center gap-2 py-3 px-4 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                <Users className="w-4 h-4" />
                <span className="hidden sm:inline">المستخدمين</span>
              </TabsTrigger>
              <TabsTrigger
                value="courses"
                className="flex items-center gap-2 py-3 px-4 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                <BookOpen className="w-4 h-4" />
                <span className="hidden sm:inline">المحاضرات</span>
              </TabsTrigger>
              <TabsTrigger
                value="certificates"
                className="flex items-center gap-2 py-3 px-4 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                <Award className="w-4 h-4" />
                <span className="hidden sm:inline">الشهادات</span>
              </TabsTrigger>
              <TabsTrigger
                value="payments"
                className="flex items-center gap-2 py-3 px-4 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                <CreditCard className="w-4 h-4" />
                <span className="hidden sm:inline">المدفوعات</span>
              </TabsTrigger>
              <TabsTrigger
                value="settings"
                className="flex items-center gap-2 py-3 px-4 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                <Settings className="w-4 h-4" />
                <span className="hidden sm:inline">الإعدادات</span>
              </TabsTrigger>
            </TabsList>

            {/* Loading Overlay */}
            {tabLoading && (
              <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
                <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-2xl border border-slate-200 dark:border-slate-700">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <div className="w-12 h-12 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-spin"></div>
                      <div className="absolute top-0 left-0 w-12 h-12 border-4 border-transparent border-t-blue-600 rounded-full animate-spin"></div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        جاري تحميل البيانات
                      </h3>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        يرجى الانتظار...
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Recent Activity */}
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <Activity className="w-5 h-5 text-blue-600" />
                      النشاط الأخير
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-slate-900 dark:text-white">
                            تم إصدار شهادة جديدة
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            أبانوب نشأت - منذ ساعتين
                          </p>
                        </div>
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                          شهادة
                        </Badge>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-slate-900 dark:text-white">
                            مستخدم جديد مسجل
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            مريم يوسف - منذ 4 ساعات
                          </p>
                        </div>
                        <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                          تسجيل
                        </Badge>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-slate-900 dark:text-white">
                            دفعة معلقة
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            يوحنا مينا - منذ يوم
                          </p>
                        </div>
                        <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
                          دفع
                        </Badge>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-slate-900 dark:text-white">
                            تم إضافة محاضرة جديدة
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            إدارة الأموال - منذ يومين
                          </p>
                        </div>
                        <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                          محتوى
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Stats */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <TrendingUp className="w-5 h-5 text-green-600" />
                      إحصائيات سريعة
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          متوسط وقت الإكمال
                        </span>
                        <span className="font-semibold">
                          {mockAdminData.stats.averageCompletionTime} يوم
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          أفضل محاضرة
                        </span>
                        <span className="font-semibold text-sm">
                          {mockAdminData.stats.topPerformingLecture}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          معدل الفشل
                        </span>
                        <span className="font-semibold text-red-600">
                          {mockAdminData.stats.failureRate}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          المستخدمين النشطين
                        </span>
                        <span className="font-semibold text-green-600">
                          {mockAdminData.stats.activeUsers}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Progress Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <BarChart3 className="w-5 h-5 text-blue-600" />
                    إحصائيات التقدم بالمحاضرات
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {mockAdminData.lectures.map((lecture) => (
                      <div key={lecture.id} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="font-medium">{lecture.title}</span>
                          <span>
                            {Math.round(
                              (lecture.completions / lecture.enrollments) * 100
                            )}
                            %
                          </span>
                        </div>
                        <Progress
                          value={
                            (lecture.completions / lecture.enrollments) * 100
                          }
                          className="h-2"
                        />
                        <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400">
                          <span>{lecture.completions} مكتمل</span>
                          <span>{lecture.enrollments} مسجل</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Enhanced Users Tab */}
            <TabsContent value="users" className="space-y-6">
              {/* Enhanced Search and Filters */}
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex flex-col lg:flex-row gap-4">
                      <div className="flex-1">
                        <div className="relative">
                          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                          <Input
                            placeholder="البحث عن المستخدمين (الاسم، البريد، الهاتف)..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pr-10"
                          />
                        </div>
                      </div>
                      <Select
                        value={filterStatus}
                        onValueChange={setFilterStatus}
                      >
                        <SelectTrigger className="w-full lg:w-48">
                          <SelectValue placeholder="تصفية حسب الحالة" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">جميع الحالات</SelectItem>
                          <SelectItem value="completed">مكتمل</SelectItem>
                          <SelectItem value="in_progress">
                            قيد التقدم
                          </SelectItem>
                          <SelectItem value="payment_pending">
                            في انتظار الدفع
                          </SelectItem>
                          <SelectItem value="suspended">معلق</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select value={sortField} onValueChange={setSortField}>
                        <SelectTrigger className="w-full lg:w-48">
                          <SelectValue placeholder="ترتيب حسب" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="registrationDate">
                            تاريخ التسجيل
                          </SelectItem>
                          <SelectItem value="name">الاسم</SelectItem>
                          <SelectItem value="progress">التقدم</SelectItem>
                          <SelectItem value="finalExamScore">
                            نتيجة الامتحان
                          </SelectItem>
                          <SelectItem value="lastActivity">آخر نشاط</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        onClick={() =>
                          setSortDirection(
                            sortDirection === "asc" ? "desc" : "asc"
                          )
                        }
                      >
                        {sortDirection === "asc" ? (
                          <SortAsc className="w-4 h-4" />
                        ) : (
                          <SortDesc className="w-4 h-4" />
                        )}
                      </Button>
                    </div>

                    {/* Bulk Actions */}
                    {selectedUsers.length > 0 && (
                      <div className="flex items-center gap-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <span className="text-sm font-medium">
                          تم تحديد {selectedUsers.length} مستخدم
                        </span>
                        <Select
                          value={bulkAction}
                          onValueChange={setBulkAction}
                        >
                          <SelectTrigger className="w-48">
                            <SelectValue placeholder="اختر إجراء جماعي" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="send_email">
                              إرسال رسالة بريدية
                            </SelectItem>
                            <SelectItem value="export_data">
                              تصدير البيانات
                            </SelectItem>
                            <SelectItem value="generate_certificates">
                              إنشاء شهادات
                            </SelectItem>
                            <SelectItem value="suspend_users">
                              تعليق المستخدمين
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <Button
                          onClick={handleBulkAction}
                          disabled={!bulkAction || actionLoading === "bulk"}
                        >
                          {actionLoading === "bulk" ? (
                            <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                          ) : null}
                          تطبيق
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setSelectedUsers([])}
                        >
                          إلغاء التحديد
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Enhanced Users Table */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-3">
                      <Users className="w-5 h-5 text-blue-600" />
                      إدارة المستخدمين ({filteredUsers.length})
                    </span>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Download className="w-4 h-4 ml-2" />
                        تصدير Excel
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowAddUserDialog(true)}
                      >
                        <Plus className="w-4 h-4 ml-2" />
                        إضافة مستخدم
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-12">
                            <Checkbox
                              checked={
                                selectedUsers.length === filteredUsers.length
                              }
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedUsers(
                                    filteredUsers.map((user) => user.id)
                                  );
                                } else {
                                  setSelectedUsers([]);
                                }
                              }}
                            />
                          </TableHead>
                          <TableHead>المستخدم</TableHead>
                          <TableHead>التقدم</TableHead>
                          <TableHead>حالة الدفع</TableHead>
                          <TableHead>النتيجة</TableHead>
                          <TableHead>الحالة</TableHead>
                          <TableHead>آخر نشاط</TableHead>
                          <TableHead>الإجراءات</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredUsers.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedUsers.includes(user.id)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setSelectedUsers([
                                      ...selectedUsers,
                                      user.id,
                                    ]);
                                  } else {
                                    setSelectedUsers(
                                      selectedUsers.filter(
                                        (id) => id !== user.id
                                      )
                                    );
                                  }
                                }}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-semibold">
                                  {user.name.charAt(0)}
                                </div>
                                <div>
                                  <p className="font-medium text-slate-900 dark:text-white">
                                    {user.name}
                                  </p>
                                  <p className="text-sm text-slate-500 dark:text-slate-400">
                                    {user.email}
                                  </p>
                                  <p className="text-xs text-slate-400 dark:text-slate-500">
                                    {user.phone}
                                  </p>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="space-y-1">
                                <div className="flex justify-between text-sm">
                                  <span>{user.progress}%</span>
                                  <span className="text-xs text-slate-500">
                                    {user.lecturesCompleted}/
                                    {user.totalLectures}
                                  </span>
                                </div>
                                <Progress
                                  value={user.progress}
                                  className="h-2 w-24"
                                />
                              </div>
                            </TableCell>
                            <TableCell>
                              {getPaymentStatusBadge(user.paymentStatus)}
                            </TableCell>
                            <TableCell>
                              {user.finalExamScore ? (
                                <div className="space-y-1">
                                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                    {user.finalExamScore}%
                                  </Badge>
                                  <p className="text-xs text-slate-500">
                                    {user.finalExamAttempts} محاولة
                                  </p>
                                </div>
                              ) : (
                                <Badge variant="outline">لم يتم</Badge>
                              )}
                            </TableCell>
                            <TableCell>{getStatusBadge(user.status)}</TableCell>
                            <TableCell>
                              <div className="space-y-1">
                                <span className="text-sm text-slate-500 dark:text-slate-400">
                                  {user.lastActivity}
                                </span>
                                <p className="text-xs text-slate-400">
                                  {Math.floor(user.timeSpent / 60)}س{" "}
                                  {user.timeSpent % 60}د
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    disabled={actionLoading?.includes(
                                      `${user.id}`
                                    )}
                                  >
                                    {actionLoading?.includes(`${user.id}`) ? (
                                      <Loader2 className="w-4 h-4 animate-spin" />
                                    ) : (
                                      <MoreHorizontal className="w-4 h-4" />
                                    )}
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>
                                    الإجراءات
                                  </DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleUserAction("view", user.id)
                                    }
                                  >
                                    <Eye className="w-4 h-4 ml-2" />
                                    عرض التفاصيل
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleUserAction("send_email", user.id)
                                    }
                                  >
                                    <Mail className="w-4 h-4 ml-2" />
                                    إرسال رسالة
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleUserAction(
                                        "download_receipt",
                                        user.id
                                      )
                                    }
                                  >
                                    <Download className="w-4 h-4 ml-2" />
                                    تحميل الإيصال
                                  </DropdownMenuItem>
                                  {user.certificateIssued && (
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleUserAction(
                                          "view_certificate",
                                          user.id
                                        )
                                      }
                                    >
                                      <Award className="w-4 h-4 ml-2" />
                                      عرض الشهادة
                                    </DropdownMenuItem>
                                  )}
                                  {!user.certificateIssued &&
                                    (user.finalExamScore || 0) >= 80 && (
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleUserAction(
                                            "generate_certificate",
                                            user.id
                                          )
                                        }
                                      >
                                        <Award className="w-4 h-4 ml-2" />
                                        إنشاء شهادة
                                      </DropdownMenuItem>
                                    )}
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleUserAction("edit", user.id)
                                    }
                                  >
                                    <Edit className="w-4 h-4 ml-2" />
                                    تعديل البيانات
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="text-amber-600"
                                    onClick={() =>
                                      handleUserAction("suspend", user.id)
                                    }
                                  >
                                    <AlertTriangle className="w-4 h-4 ml-2" />
                                    تعليق المستخدم
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="text-red-600"
                                    onClick={() =>
                                      handleUserAction("delete", user.id)
                                    }
                                  >
                                    <Trash2 className="w-4 h-4 ml-2" />
                                    حذف المستخدم
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Enhanced Courses Tab */}
            <TabsContent value="courses" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
                  إدارة المحاضرات والاختبارات
                </h2>
                <div className="flex gap-2">
                  <Button
                    className="bg-green-600 hover:bg-green-700"
                    onClick={() => setNewLecture({})}
                    disabled={actionLoading === "new_lecture"}
                  >
                    {actionLoading === "new_lecture" ? (
                      <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                    ) : (
                      <Plus className="w-4 h-4 ml-2" />
                    )}
                    إضافة محاضرة جديدة
                  </Button>
                  <Button variant="outline">
                    <Settings className="w-4 h-4 ml-2" />
                    إدارة الاختبار النهائي
                  </Button>
                </div>
              </div>

              <div className="grid gap-6">
                {mockAdminData.lectures.map((lecture) => (
                  <Card key={lecture.id}>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Lecture Header */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center text-white font-bold">
                              {lecture.order}
                            </div>
                            <div>
                              <h3 className="font-bold text-slate-900 dark:text-white text-lg">
                                {lecture.title}
                              </h3>
                              <p className="text-slate-600 dark:text-slate-400">
                                {lecture.titleEn}
                              </p>
                              <p className="text-sm text-slate-500 dark:text-slate-500 mt-1">
                                {lecture.description}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="text-center">
                              <p className="text-2xl font-bold text-slate-900 dark:text-white">
                                {lecture.enrollments}
                              </p>
                              <p className="text-sm text-slate-500 dark:text-slate-400">
                                مسجل
                              </p>
                            </div>
                            <div className="text-center">
                              <p className="text-2xl font-bold text-green-600">
                                {lecture.completions}
                              </p>
                              <p className="text-sm text-slate-500 dark:text-slate-400">
                                مكتمل
                              </p>
                            </div>
                            <div className="text-center">
                              <p className="text-2xl font-bold text-blue-600">
                                {lecture.averageScore}%
                              </p>
                              <p className="text-sm text-slate-500 dark:text-slate-400">
                                متوسط الدرجات
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Lecture Details */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">
                              تفاصيل المحاضرة
                            </Label>
                            <div className="flex items-center gap-4">
                              <Badge variant="outline">
                                <Clock className="w-3 h-3 ml-1" />
                                {lecture.duration} دقيقة
                              </Badge>
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                {lecture.status === "published"
                                  ? "منشور"
                                  : "مسودة"}
                              </Badge>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">
                              المواد التعليمية
                            </Label>
                            <div className="flex gap-2">
                              {lecture.materials.map((material, index) => (
                                <Badge
                                  key={index}
                                  variant="outline"
                                  className="text-xs"
                                >
                                  {material.type === "pdf" && (
                                    <FilePdf className="w-3 h-3 ml-1" />
                                  )}
                                  {material.type === "ppt" && (
                                    <FileImage className="w-3 h-3 ml-1" />
                                  )}
                                  {material.name}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">
                              الاختبار
                            </Label>
                            <div className="flex items-center gap-2">
                              <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                                {lecture.quiz.questions.length} سؤال
                              </Badge>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  setEditingQuestion({
                                    lectureId: lecture.id,
                                    quiz: lecture.quiz,
                                  })
                                }
                                disabled={actionLoading?.includes(
                                  `lecture-${lecture.id}`
                                )}
                              >
                                <Edit className="w-3 h-3 ml-1" />
                                تعديل
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 pt-4 border-t border-slate-200 dark:border-slate-700">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleLectureAction("edit", lecture.id)
                            }
                            disabled={
                              actionLoading === `lecture-edit-${lecture.id}`
                            }
                          >
                            {actionLoading === `lecture-edit-${lecture.id}` ? (
                              <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                            ) : (
                              <Edit className="w-4 h-4 ml-2" />
                            )}
                            تعديل المحاضرة
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleLectureAction("preview", lecture.id)
                            }
                            disabled={
                              actionLoading === `lecture-preview-${lecture.id}`
                            }
                          >
                            {actionLoading ===
                            `lecture-preview-${lecture.id}` ? (
                              <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                            ) : (
                              <Eye className="w-4 h-4 ml-2" />
                            )}
                            معاينة
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleLectureAction("upload_video", lecture.id)
                            }
                            disabled={
                              actionLoading ===
                              `lecture-upload_video-${lecture.id}`
                            }
                          >
                            {actionLoading ===
                            `lecture-upload_video-${lecture.id}` ? (
                              <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                            ) : (
                              <Upload className="w-4 h-4 ml-2" />
                            )}
                            رفع فيديو
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleLectureAction("view_stats", lecture.id)
                            }
                            disabled={
                              actionLoading ===
                              `lecture-view_stats-${lecture.id}`
                            }
                          >
                            {actionLoading ===
                            `lecture-view_stats-${lecture.id}` ? (
                              <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                            ) : (
                              <BarChart3 className="w-4 h-4 ml-2" />
                            )}
                            الإحصائيات
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 hover:text-red-700 bg-transparent"
                                disabled={
                                  actionLoading ===
                                  `lecture-delete-${lecture.id}`
                                }
                              >
                                {actionLoading ===
                                `lecture-delete-${lecture.id}` ? (
                                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                                ) : (
                                  <Trash2 className="w-4 h-4 ml-2" />
                                )}
                                حذف
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                                <AlertDialogDescription>
                                  هل أنت متأكد من حذف هذه المحاضرة؟ لا يمكن
                                  التراجع عن هذا الإجراء.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                <AlertDialogAction
                                  className="bg-red-600 hover:bg-red-700"
                                  onClick={() =>
                                    handleLectureAction("delete", lecture.id)
                                  }
                                >
                                  حذف
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Final Exam Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <GraduationCap className="w-5 h-5 text-purple-600" />
                    إدارة الاختبار النهائي
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600">
                        {mockAdminData.finalExam.questions.length}
                      </p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        إجمالي الأسئلة
                      </p>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">
                        {mockAdminData.finalExam.passingScore}%
                      </p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        درجة النجاح
                      </p>
                    </div>
                    <div className="text-center p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-amber-600">
                        {mockAdminData.finalExam.duration}
                      </p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        دقيقة
                      </p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-purple-600">
                        {mockAdminData.finalExam.maxAttempts}
                      </p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        محاولات مسموحة
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      className="bg-purple-600 hover:bg-purple-700"
                      onClick={() => setNewQuestion({ examType: "final" })}
                    >
                      <Plus className="w-4 h-4 ml-2" />
                      إضافة سؤال جديد
                    </Button>
                    <Button variant="outline">
                      <Edit className="w-4 h-4 ml-2" />
                      تعديل الإعدادات
                    </Button>
                    <Button variant="outline">
                      <BarChart3 className="w-4 h-4 ml-2" />
                      إحصائيات الأداء
                    </Button>
                  </div>

                  {/* Final Exam Questions */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      أسئلة الاختبار النهائي
                    </Label>
                    {mockAdminData.finalExam.questions.map(
                      (question, index) => (
                        <div
                          key={question.id}
                          className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant="outline">
                                  سؤال {index + 1}
                                </Badge>
                                {getDifficultyBadge(question.difficulty)}
                                <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                                  {question.category}
                                </Badge>
                              </div>
                              <p className="font-medium text-slate-900 dark:text-white mb-2">
                                {question.question}
                              </p>
                              <div className="space-y-1">
                                {question.options.map((option, optionIndex) => (
                                  <div
                                    key={optionIndex}
                                    className={`text-sm p-2 rounded ${
                                      optionIndex === question.correctAnswer
                                        ? "bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300"
                                        : "bg-slate-50 dark:bg-slate-800 text-slate-600 dark:text-slate-400"
                                    }`}
                                  >
                                    {String.fromCharCode(65 + optionIndex)}.{" "}
                                    {option}
                                    {optionIndex === question.correctAnswer && (
                                      <CheckCircle className="w-4 h-4 inline mr-2 text-green-600" />
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  setEditingQuestion({
                                    examType: "final",
                                    question,
                                  })
                                }
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-red-600 bg-transparent"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      حذف السؤال
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                      هل أنت متأكد من حذف هذا السؤال؟ لا يمكن
                                      التراجع عن هذا الإجراء.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                    <AlertDialogAction className="bg-red-600 hover:bg-red-700">
                                      حذف
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Enhanced Certificates Tab */}
            <TabsContent value="certificates" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Certificate Verification */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <QrCode className="w-5 h-5 text-blue-600" />
                      التحقق من الشهادات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <Label htmlFor="certificate-id">
                        رقم الشهادة أو امسح الـ QR Code
                      </Label>
                      <div className="flex gap-2">
                        <Input
                          id="certificate-id"
                          placeholder="CERT-2024-001234"
                          value={certificateVerification}
                          onChange={(e) =>
                            setCertificateVerification(e.target.value)
                          }
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          onClick={() => setQrScannerOpen(true)}
                          className="px-3"
                        >
                          <Camera className="w-4 h-4" />
                        </Button>
                      </div>
                      <Button
                        onClick={handleCertificateVerification}
                        disabled={isScanning || !certificateVerification.trim()}
                        className="w-full bg-blue-600 hover:bg-blue-700"
                      >
                        {isScanning ? (
                          <>
                            <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
                            جاري التحقق...
                          </>
                        ) : (
                          <>
                            <Scan className="w-4 h-4 ml-2" />
                            التحقق من الشهادة
                          </>
                        )}
                      </Button>
                    </div>

                    {verificationResult && (
                      <div className="mt-4 p-4 rounded-lg border">
                        {verificationResult.error ? (
                          <div className="flex items-center gap-3 text-red-600">
                            <XCircle className="w-5 h-5" />
                            <span>الشهادة غير موجودة أو غير صالحة</span>
                          </div>
                        ) : (
                          <div className="space-y-3">
                            <div className="flex items-center gap-3 text-green-600">
                              <CheckCircle className="w-5 h-5" />
                              <span className="font-semibold">
                                شهادة صالحة ومعتمدة
                              </span>
                            </div>
                            <div className="space-y-2 text-sm">
                              <p>
                                <strong>اسم الطالب:</strong>{" "}
                                {verificationResult.studentName}
                              </p>
                              <p>
                                <strong>البريد الإلكتروني:</strong>{" "}
                                {verificationResult.studentEmail}
                              </p>
                              <p>
                                <strong>تاريخ الإصدار:</strong>{" "}
                                {verificationResult.issueDate}
                              </p>
                              <p>
                                <strong>الدرجة:</strong>{" "}
                                {verificationResult.score}%
                              </p>
                              <p>
                                <strong>عدد مرات التحقق:</strong>{" "}
                                {verificationResult.verificationCount}
                              </p>
                              <p>
                                <strong>آخر تحقق:</strong>{" "}
                                {verificationResult.lastVerified}
                              </p>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  handleCertificateAction(
                                    "download",
                                    verificationResult.id
                                  )
                                }
                                disabled={
                                  actionLoading ===
                                  `cert-download-${verificationResult.id}`
                                }
                              >
                                {actionLoading ===
                                `cert-download-${verificationResult.id}` ? (
                                  <Loader2 className="w-3 h-3 ml-1 animate-spin" />
                                ) : (
                                  <Download className="w-3 h-3 ml-1" />
                                )}
                                تحميل الشهادة
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  handleCertificateAction(
                                    "copy_link",
                                    verificationResult.id
                                  )
                                }
                                disabled={
                                  actionLoading ===
                                  `cert-copy_link-${verificationResult.id}`
                                }
                              >
                                {actionLoading ===
                                `cert-copy_link-${verificationResult.id}` ? (
                                  <Loader2 className="w-3 h-3 ml-1 animate-spin" />
                                ) : (
                                  <Copy className="w-3 h-3 ml-1" />
                                )}
                                نسخ الرابط
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Certificate Statistics */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <Award className="w-5 h-5 text-amber-600" />
                      إحصائيات الشهادات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <p className="text-2xl font-bold text-green-600">
                          {mockAdminData.stats.certificatesIssued}
                        </p>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          شهادة صادرة
                        </p>
                      </div>
                      <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <p className="text-2xl font-bold text-blue-600">
                          {mockAdminData.stats.averageScore}%
                        </p>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          متوسط الدرجات
                        </p>
                      </div>
                      <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <p className="text-2xl font-bold text-purple-600">
                          {mockAdminData.certificates.reduce(
                            (sum, cert) => sum + cert.verificationCount,
                            0
                          )}
                        </p>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          إجمالي التحققات
                        </p>
                      </div>
                      <div className="text-center p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                        <p className="text-2xl font-bold text-amber-600">
                          {mockAdminData.certificates.reduce(
                            (sum, cert) => sum + cert.downloadCount,
                            0
                          )}
                        </p>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          مرات التحميل
                        </p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>معدل النجاح</span>
                        <span>94%</span>
                      </div>
                      <Progress value={94} className="h-2" />
                    </div>
                    <Button
                      className="w-full bg-green-600 hover:bg-green-700"
                      onClick={() => setShowManualCertificateDialog(true)}
                      disabled={actionLoading === "create_certificate"}
                    >
                      {actionLoading === "create_certificate" ? (
                        <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                      ) : (
                        <Plus className="w-4 h-4 ml-2" />
                      )}
                      إنشاء شهادة يدوياً
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Certificates List */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-3">
                      <FileText className="w-5 h-5 text-blue-600" />
                      الشهادات الصادرة ({mockAdminData.certificates.length})
                    </span>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Download className="w-4 h-4 ml-2" />
                        تصدير القائمة
                      </Button>
                      <Button size="sm" variant="outline">
                        <RefreshCw className="w-4 h-4 ml-2" />
                        تحديث
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>رقم الشهادة</TableHead>
                          <TableHead>اسم الطالب</TableHead>
                          <TableHead>تاريخ الإصدار</TableHead>
                          <TableHead>الدرجة</TableHead>
                          <TableHead>الحالة</TableHead>
                          <TableHead>التحققات</TableHead>
                          <TableHead>التحميلات</TableHead>
                          <TableHead>الإجراءات</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockAdminData.certificates.map((cert) => (
                          <TableRow key={cert.id}>
                            <TableCell>
                              <code className="text-sm bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                                {cert.id}
                              </code>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="font-medium text-slate-900 dark:text-white">
                                  {cert.studentName}
                                </p>
                                <p className="text-sm text-slate-500 dark:text-slate-400">
                                  {cert.studentEmail}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>{cert.issueDate}</TableCell>
                            <TableCell>
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                {cert.score}%
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                <CheckCircle className="w-3 h-3 ml-1" />
                                صالحة
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="text-center">
                                <p className="font-semibold">
                                  {cert.verificationCount}
                                </p>
                                <p className="text-xs text-slate-500">
                                  آخر: {cert.lastVerified}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-center">
                                <p className="font-semibold">
                                  {cert.downloadCount}
                                </p>
                                <p className="text-xs text-slate-500">مرة</p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleCertificateAction("view", cert.id)
                                  }
                                  disabled={
                                    actionLoading === `cert-view-${cert.id}`
                                  }
                                >
                                  {actionLoading === `cert-view-${cert.id}` ? (
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <Eye className="w-4 h-4" />
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleCertificateAction("download", cert.id)
                                  }
                                  disabled={
                                    actionLoading === `cert-download-${cert.id}`
                                  }
                                >
                                  {actionLoading ===
                                  `cert-download-${cert.id}` ? (
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <Download className="w-4 h-4" />
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleCertificateAction("share", cert.id)
                                  }
                                  disabled={
                                    actionLoading === `cert-share-${cert.id}`
                                  }
                                >
                                  {actionLoading === `cert-share-${cert.id}` ? (
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <ExternalLink className="w-4 h-4" />
                                  )}
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="outline" size="sm">
                                      <MoreHorizontal className="w-4 h-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleCertificateAction(
                                          "copy_link",
                                          cert.id
                                        )
                                      }
                                    >
                                      <Copy className="w-4 h-4 ml-2" />
                                      نسخ الرابط
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleCertificateAction(
                                          "send_email",
                                          cert.id
                                        )
                                      }
                                    >
                                      <Send className="w-4 h-4 ml-2" />
                                      إرسال بالبريد
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      className="text-red-600"
                                      onClick={() =>
                                        handleCertificateAction(
                                          "revoke",
                                          cert.id
                                        )
                                      }
                                    >
                                      <XCircle className="w-4 h-4 ml-2" />
                                      إلغاء الشهادة
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Enhanced Payments Tab */}
            <TabsContent value="payments" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <CreditCard className="w-5 h-5 text-green-600" />
                    إدارة المدفوعات
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">
                        {mockAdminData.stats.totalRevenue.toLocaleString()} ج.م
                      </p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        إجمالي الإيرادات
                      </p>
                    </div>
                    <div className="text-center p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-amber-600">
                        {mockAdminData.stats.pendingPayments}
                      </p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        دفعات معلقة
                      </p>
                    </div>
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600">
                        {
                          mockAdminData.payments.filter(
                            (p) => p.status === "completed"
                          ).length
                        }
                      </p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        دفعات مكتملة
                      </p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-purple-600">
                        200 ج.م
                      </p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        متوسط قيمة الدفعة
                      </p>
                    </div>
                  </div>

                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>المستخدم</TableHead>
                          <TableHead>المبلغ</TableHead>
                          <TableHead>تاريخ الدفع</TableHead>
                          <TableHead>الحالة</TableHead>
                          <TableHead>طريقة الدفع</TableHead>
                          <TableHead>رقم المعاملة</TableHead>
                          <TableHead>الإجراءات</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockAdminData.payments.map((payment) => {
                          const user = mockAdminData.users.find(
                            (u) => u.id === payment.userId
                          );
                          return (
                            <TableRow key={payment.id}>
                              <TableCell>
                                {user && (
                                  <div className="flex items-center gap-3">
                                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                      {user.name.charAt(0)}
                                    </div>
                                    <div>
                                      <p className="font-medium text-slate-900 dark:text-white">
                                        {user.name}
                                      </p>
                                      <p className="text-sm text-slate-500 dark:text-slate-400">
                                        {user.email}
                                      </p>
                                    </div>
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>
                                <span className="font-semibold">
                                  {payment.amount} {payment.currency}
                                </span>
                              </TableCell>
                              <TableCell>
                                {payment.date || "غير محدد"}
                              </TableCell>
                              <TableCell>
                                {getPaymentStatusBadge(payment.status)}
                              </TableCell>
                              <TableCell>
                                {payment.method ? (
                                  <Badge
                                    variant="outline"
                                    className="capitalize"
                                  >
                                    {payment.method}
                                  </Badge>
                                ) : (
                                  <span className="text-slate-400">
                                    غير محدد
                                  </span>
                                )}
                              </TableCell>
                              <TableCell>
                                {payment.transactionId ? (
                                  <code className="text-sm bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                                    {payment.transactionId}
                                  </code>
                                ) : (
                                  <span className="text-slate-400">
                                    غير متوفر
                                  </span>
                                )}
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  {payment.receiptUrl && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() =>
                                        handlePaymentAction(
                                          "download_receipt",
                                          payment.id
                                        )
                                      }
                                      disabled={
                                        actionLoading ===
                                        `payment-download_receipt-${payment.id}`
                                      }
                                    >
                                      {actionLoading ===
                                      `payment-download_receipt-${payment.id}` ? (
                                        <Loader2 className="w-4 h-4 animate-spin" />
                                      ) : (
                                        <Download className="w-4 h-4" />
                                      )}
                                    </Button>
                                  )}
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      handlePaymentAction(
                                        "view_details",
                                        payment.id
                                      )
                                    }
                                    disabled={
                                      actionLoading ===
                                      `payment-view_details-${payment.id}`
                                    }
                                  >
                                    {actionLoading ===
                                    `payment-view_details-${payment.id}` ? (
                                      <Loader2 className="w-4 h-4 animate-spin" />
                                    ) : (
                                      <Eye className="w-4 h-4" />
                                    )}
                                  </Button>
                                  {payment.status === "pending" && (
                                    <Button
                                      size="sm"
                                      className="bg-green-600 hover:bg-green-700"
                                      onClick={() =>
                                        handlePaymentAction(
                                          "approve_payment",
                                          payment.id
                                        )
                                      }
                                      disabled={
                                        actionLoading ===
                                        `payment-approve_payment-${payment.id}`
                                      }
                                    >
                                      {actionLoading ===
                                      `payment-approve_payment-${payment.id}` ? (
                                        <Loader2 className="w-4 h-4 animate-spin" />
                                      ) : (
                                        <CheckCircle className="w-4 h-4" />
                                      )}
                                    </Button>
                                  )}
                                  {payment.status === "completed" && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-red-600 bg-transparent"
                                      onClick={() =>
                                        handlePaymentAction(
                                          "refund",
                                          payment.id
                                        )
                                      }
                                      disabled={
                                        actionLoading ===
                                        `payment-refund-${payment.id}`
                                      }
                                    >
                                      {actionLoading ===
                                      `payment-refund-${payment.id}` ? (
                                        <Loader2 className="w-4 h-4 animate-spin" />
                                      ) : (
                                        <RefreshCw className="w-4 h-4" />
                                      )}
                                    </Button>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Enhanced Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* System Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <Settings className="w-5 h-5 text-blue-600" />
                      إعدادات النظام
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="course-price">سعر الكورس (ج.م)</Label>
                        <Input
                          id="course-price"
                          type="number"
                          value={systemSettings.coursePrice}
                          onChange={(e) =>
                            setSystemSettings({
                              ...systemSettings,
                              coursePrice: Number(e.target.value),
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="passing-score">
                          الدرجة المطلوبة للنجاح (%)
                        </Label>
                        <Input
                          id="passing-score"
                          type="number"
                          value={systemSettings.passingScore}
                          onChange={(e) =>
                            setSystemSettings({
                              ...systemSettings,
                              passingScore: Number(e.target.value),
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="exam-duration">
                          مدة الاختبار النهائي (دقيقة)
                        </Label>
                        <Input
                          id="exam-duration"
                          type="number"
                          value={systemSettings.examDuration}
                          onChange={(e) =>
                            setSystemSettings({
                              ...systemSettings,
                              examDuration: Number(e.target.value),
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="max-attempts">
                          عدد المحاولات المسموحة
                        </Label>
                        <Input
                          id="max-attempts"
                          type="number"
                          value={systemSettings.maxAttempts}
                          onChange={(e) =>
                            setSystemSettings({
                              ...systemSettings,
                              maxAttempts: Number(e.target.value),
                            })
                          }
                        />
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="auto-approve">
                            الموافقة التلقائية على المدفوعات
                          </Label>
                          <Switch
                            id="auto-approve"
                            checked={systemSettings.autoApprovePayments}
                            onCheckedChange={(checked) =>
                              setSystemSettings({
                                ...systemSettings,
                                autoApprovePayments: checked,
                              })
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="email-notifications">
                            إشعارات البريد الإلكتروني
                          </Label>
                          <Switch
                            id="email-notifications"
                            checked={systemSettings.emailNotifications}
                            onCheckedChange={(checked) =>
                              setSystemSettings({
                                ...systemSettings,
                                emailNotifications: checked,
                              })
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="sms-notifications">
                            إشعارات الرسائل النصية
                          </Label>
                          <Switch
                            id="sms-notifications"
                            checked={systemSettings.smsNotifications}
                            onCheckedChange={(checked) =>
                              setSystemSettings({
                                ...systemSettings,
                                smsNotifications: checked,
                              })
                            }
                          />
                        </div>
                      </div>
                    </div>
                    <Button
                      onClick={handleSaveSettings}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      disabled={actionLoading === "save_settings"}
                    >
                      {actionLoading === "save_settings" ? (
                        <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                      ) : (
                        <Save className="w-4 h-4 ml-2" />
                      )}
                      حفظ الإعدادات
                    </Button>
                  </CardContent>
                </Card>

                {/* Data Management */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <Database className="w-5 h-5 text-green-600" />
                      إدارة البيانات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <Button
                        variant="outline"
                        className="w-full justify-start bg-transparent"
                      >
                        <Download className="w-4 h-4 ml-2" />
                        تصدير جميع البيانات
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start bg-transparent"
                      >
                        <Upload className="w-4 h-4 ml-2" />
                        استيراد البيانات
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start bg-transparent"
                      >
                        <RefreshCw className="w-4 h-4 ml-2" />
                        تحديث الإحصائيات
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-amber-600 bg-transparent"
                          >
                            <AlertTriangle className="w-4 h-4 ml-2" />
                            إعادة تعيين الإحصائيات
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              إعادة تعيين الإحصائيات
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              هل أنت متأكد من إعادة تعيين جميع الإحصائيات؟ سيتم
                              حذف جميع البيانات الإحصائية.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            <AlertDialogAction className="bg-amber-600 hover:bg-amber-700">
                              إعادة تعيين
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-red-600 bg-transparent"
                          >
                            <Trash2 className="w-4 h-4 ml-2" />
                            حذف البيانات القديمة
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              حذف البيانات القديمة
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              هل أنت متأكد من حذف البيانات الأقدم من 6 أشهر؟ لا
                              يمكن التراجع عن هذا الإجراء.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            <AlertDialogAction className="bg-red-600 hover:bg-red-700">
                              حذف
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </CardContent>
                </Card>

                {/* Admin Management */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <Shield className="w-5 h-5 text-purple-600" />
                      إدارة المديرين
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <Button
                        variant="outline"
                        className="w-full justify-start bg-transparent"
                      >
                        <UserCheck className="w-4 h-4 ml-2" />
                        إضافة مدير جديد
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start bg-transparent"
                      >
                        <Key className="w-4 h-4 ml-2" />
                        تغيير كلمة المرور
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start bg-transparent"
                      >
                        <Activity className="w-4 h-4 ml-2" />
                        سجل النشاطات
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start bg-transparent"
                      >
                        <Bell className="w-4 h-4 ml-2" />
                        إعدادات الإشعارات
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* System Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <Activity className="w-5 h-5 text-indigo-600" />
                      معلومات النظام
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-slate-400">
                          إصدار النظام:
                        </span>
                        <span className="font-medium">v2.1.0</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-slate-400">
                          آخر تحديث:
                        </span>
                        <span className="font-medium">2024-01-22</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-slate-400">
                          حالة الخادم:
                        </span>
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                          متصل
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-slate-400">
                          استخدام قاعدة البيانات:
                        </span>
                        <span className="font-medium">2.3 GB</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-slate-400">
                          النسخ الاحتياطية:
                        </span>
                        <span className="font-medium">يومياً في 2:00 ص</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      {/* Enhanced User Details Modal */}
      <Dialog open={!!selectedUser} onOpenChange={() => setSelectedUser(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تفاصيل المستخدم</DialogTitle>
            <DialogDescription>
              معلومات شاملة عن المستخدم وتقدمه في الكورس
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-6">
              {/* User Header */}
              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xl font-bold">
                  {selectedUser.name.charAt(0)}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white">
                    {selectedUser.name}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400">
                    {selectedUser.nameEn}
                  </p>
                  <div className="flex items-center gap-4 mt-2">
                    <span className="flex items-center gap-1 text-sm text-slate-500">
                      <Mail className="w-4 h-4" />
                      {selectedUser.email}
                    </span>
                    <span className="flex items-center gap-1 text-sm text-slate-500">
                      <Phone className="w-4 h-4" />
                      {selectedUser.phone}
                    </span>
                    <span className="flex items-center gap-1 text-sm text-slate-500">
                      <MapPin className="w-4 h-4" />
                      {selectedUser.location}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  {getStatusBadge(selectedUser.status)}
                  <p className="text-sm text-slate-500 mt-1">
                    ID: {selectedUser.id}
                  </p>
                </div>
              </div>

              {/* User Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">
                    {selectedUser.progress}%
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    التقدم
                  </p>
                </div>
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">
                    {selectedUser.finalExamScore || "لم يتم"}
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    نتيجة الامتحان
                  </p>
                </div>
                <div className="text-center p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                  <p className="text-2xl font-bold text-amber-600">
                    {Math.floor(selectedUser.timeSpent / 60)}
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    ساعات الدراسة
                  </p>
                </div>
                <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">
                    {selectedUser.lecturesCompleted}/
                    {selectedUser.totalLectures}
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    المحاضرات
                  </p>
                </div>
              </div>

              {/* Detailed Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-900 dark:text-white">
                    المعلومات الأساسية
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <Label>تاريخ التسجيل</Label>
                      <p className="text-slate-900 dark:text-white">
                        {selectedUser.registrationDate}
                      </p>
                    </div>
                    <div>
                      <Label>آخر نشاط</Label>
                      <p className="text-slate-900 dark:text-white">
                        {selectedUser.lastActivity}
                      </p>
                    </div>
                    <div>
                      <Label>حالة الدفع</Label>
                      <div className="mt-1">
                        {getPaymentStatusBadge(selectedUser.paymentStatus)}
                      </div>
                    </div>
                    <div>
                      <Label>المبلغ المدفوع</Label>
                      <p className="text-slate-900 dark:text-white">
                        {selectedUser.paymentAmount} ج.م
                      </p>
                    </div>
                    {selectedUser.paymentDate && (
                      <div>
                        <Label>تاريخ الدفع</Label>
                        <p className="text-slate-900 dark:text-white">
                          {selectedUser.paymentDate}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-900 dark:text-white">
                    الأداء الأكاديمي
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <Label>التقدم في الكورس</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Progress
                          value={selectedUser.progress}
                          className="flex-1"
                        />
                        <span className="text-sm font-medium">
                          {selectedUser.progress}%
                        </span>
                      </div>
                    </div>
                    <div>
                      <Label>درجات الاختبارات</Label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {selectedUser.quizScores.map(
                          (score: number, index: number) => (
                            <Badge key={index} variant="outline">
                              Q{index + 1}: {score}%
                            </Badge>
                          )
                        )}
                      </div>
                    </div>
                    <div>
                      <Label>نتيجة الاختبار النهائي</Label>
                      <p className="text-slate-900 dark:text-white">
                        {selectedUser.finalExamScore ? (
                          <>
                            {selectedUser.finalExamScore}% (
                            {selectedUser.finalExamAttempts} محاولة)
                          </>
                        ) : (
                          "لم يتم بعد"
                        )}
                      </p>
                    </div>
                    <div>
                      <Label>حالة الشهادة</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-slate-900 dark:text-white">
                          {selectedUser.certificateIssued
                            ? "تم الإصدار"
                            : "لم يتم الإصدار"}
                        </p>
                        {selectedUser.certificateId && (
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                            {selectedUser.certificateId}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes Section */}
              {selectedUser.notes && (
                <div className="space-y-2">
                  <Label>ملاحظات</Label>
                  <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                    <p className="text-slate-900 dark:text-white">
                      {selectedUser.notes}
                    </p>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
                <Button
                  className="bg-blue-600 hover:bg-blue-700"
                  onClick={() =>
                    handleUserAction("send_email", selectedUser.id)
                  }
                  disabled={actionLoading === `send_email-${selectedUser.id}`}
                >
                  {actionLoading === `send_email-${selectedUser.id}` ? (
                    <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  ) : (
                    <Mail className="w-4 h-4 ml-2" />
                  )}
                  إرسال رسالة
                </Button>
                <Button
                  variant="outline"
                  onClick={() =>
                    handleUserAction("download_receipt", selectedUser.id)
                  }
                  disabled={
                    actionLoading === `download_receipt-${selectedUser.id}`
                  }
                >
                  {actionLoading === `download_receipt-${selectedUser.id}` ? (
                    <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  ) : (
                    <Download className="w-4 h-4 ml-2" />
                  )}
                  تحميل التقرير
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleUserAction("edit", selectedUser.id)}
                  disabled={actionLoading === `edit-${selectedUser.id}`}
                >
                  {actionLoading === `edit-${selectedUser.id}` ? (
                    <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  ) : (
                    <Edit className="w-4 h-4 ml-2" />
                  )}
                  تعديل البيانات
                </Button>
                {selectedUser.certificateIssued && (
                  <Button
                    variant="outline"
                    onClick={() =>
                      handleUserAction("view_certificate", selectedUser.id)
                    }
                    disabled={
                      actionLoading === `view_certificate-${selectedUser.id}`
                    }
                  >
                    {actionLoading === `view_certificate-${selectedUser.id}` ? (
                      <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                    ) : (
                      <Award className="w-4 h-4 ml-2" />
                    )}
                    عرض الشهادة
                  </Button>
                )}
                {!selectedUser.certificateIssued &&
                  selectedUser.finalExamScore >= 80 && (
                    <Button
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() =>
                        handleUserAction(
                          "generate_certificate",
                          selectedUser.id
                        )
                      }
                      disabled={
                        actionLoading ===
                        `generate_certificate-${selectedUser.id}`
                      }
                    >
                      {actionLoading ===
                      `generate_certificate-${selectedUser.id}` ? (
                        <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                      ) : (
                        <Award className="w-4 h-4 ml-2" />
                      )}
                      إنشاء شهادة
                    </Button>
                  )}
                <Button
                  variant="outline"
                  className="text-amber-600 bg-transparent"
                  onClick={() => handleUserAction("suspend", selectedUser.id)}
                  disabled={actionLoading === `suspend-${selectedUser.id}`}
                >
                  {actionLoading === `suspend-${selectedUser.id}` ? (
                    <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  ) : (
                    <AlertTriangle className="w-4 h-4 ml-2" />
                  )}
                  تعليق المستخدم
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add User Dialog */}
      <Dialog open={showAddUserDialog} onOpenChange={setShowAddUserDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>إضافة مستخدم جديد</DialogTitle>
            <DialogDescription>إدخال بيانات المستخدم الجديد</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-user-name">الاسم (عربي)</Label>
                <Input
                  id="new-user-name"
                  value={newUser.name}
                  onChange={(e) =>
                    setNewUser({ ...newUser, name: e.target.value })
                  }
                  placeholder="أدخل الاسم بالعربية"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-user-name-en">الاسم (إنجليزي)</Label>
                <Input
                  id="new-user-name-en"
                  value={newUser.nameEn}
                  onChange={(e) =>
                    setNewUser({ ...newUser, nameEn: e.target.value })
                  }
                  placeholder="Enter name in English"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-user-email">البريد الإلكتروني</Label>
                <Input
                  id="new-user-email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) =>
                    setNewUser({ ...newUser, email: e.target.value })
                  }
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-user-phone">رقم الهاتف</Label>
                <Input
                  id="new-user-phone"
                  value={newUser.phone}
                  onChange={(e) =>
                    setNewUser({ ...newUser, phone: e.target.value })
                  }
                  placeholder="+20 ************"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-user-location">المحافظة</Label>
                <Input
                  id="new-user-location"
                  value={newUser.location}
                  onChange={(e) =>
                    setNewUser({ ...newUser, location: e.target.value })
                  }
                  placeholder="القاهرة"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-user-notes">ملاحظات</Label>
              <Textarea
                id="new-user-notes"
                value={newUser.notes}
                onChange={(e) =>
                  setNewUser({ ...newUser, notes: e.target.value })
                }
                placeholder="أي ملاحظات إضافية..."
                rows={3}
              />
            </div>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowAddUserDialog(false)}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleAddUser}
                disabled={
                  actionLoading === "add_user" ||
                  !newUser.name ||
                  !newUser.email
                }
              >
                {actionLoading === "add_user" ? (
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                ) : (
                  <UserPlus className="w-4 h-4 ml-2" />
                )}
                إضافة المستخدم
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={showEditUserDialog} onOpenChange={setShowEditUserDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>تعديل بيانات المستخدم</DialogTitle>
            <DialogDescription>تحديث معلومات المستخدم</DialogDescription>
          </DialogHeader>
          {editingUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-user-name">الاسم (عربي)</Label>
                  <Input
                    id="edit-user-name"
                    defaultValue={editingUser.name}
                    placeholder="أدخل الاسم بالعربية"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-user-name-en">الاسم (إنجليزي)</Label>
                  <Input
                    id="edit-user-name-en"
                    defaultValue={editingUser.nameEn}
                    placeholder="Enter name in English"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-user-email">البريد الإلكتروني</Label>
                  <Input
                    id="edit-user-email"
                    type="email"
                    defaultValue={editingUser.email}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-user-phone">رقم الهاتف</Label>
                  <Input
                    id="edit-user-phone"
                    defaultValue={editingUser.phone}
                    placeholder="+20 ************"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-user-location">المحافظة</Label>
                  <Input
                    id="edit-user-location"
                    defaultValue={editingUser.location}
                    placeholder="القاهرة"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-user-status">الحالة</Label>
                  <Select defaultValue={editingUser.status}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="completed">مكتمل</SelectItem>
                      <SelectItem value="in_progress">قيد التقدم</SelectItem>
                      <SelectItem value="payment_pending">
                        في انتظار الدفع
                      </SelectItem>
                      <SelectItem value="suspended">معلق</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-user-notes">ملاحظات</Label>
                <Textarea
                  id="edit-user-notes"
                  defaultValue={editingUser.notes}
                  placeholder="أي ملاحظات إضافية..."
                  rows={3}
                />
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowEditUserDialog(false)}
                >
                  إلغاء
                </Button>
                <Button
                  onClick={handleEditUser}
                  disabled={actionLoading === "edit_user"}
                >
                  {actionLoading === "edit_user" ? (
                    <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4 ml-2" />
                  )}
                  حفظ التغييرات
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Manual Certificate Creation Dialog */}
      <Dialog
        open={showManualCertificateDialog}
        onOpenChange={setShowManualCertificateDialog}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              <Sparkles className="w-5 h-5 text-amber-600" />
              إنشاء شهادة يدوياً
            </DialogTitle>
            <DialogDescription>
              إنشاء شهادة جديدة لطالب بشكل يدوي
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="cert-student-name">اسم الطالب</Label>
                <Input
                  id="cert-student-name"
                  value={manualCertificate.studentName}
                  onChange={(e) =>
                    setManualCertificate({
                      ...manualCertificate,
                      studentName: e.target.value,
                    })
                  }
                  placeholder="أدخل اسم الطالب"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cert-student-email">البريد الإلكتروني</Label>
                <Input
                  id="cert-student-email"
                  type="email"
                  value={manualCertificate.studentEmail}
                  onChange={(e) =>
                    setManualCertificate({
                      ...manualCertificate,
                      studentEmail: e.target.value,
                    })
                  }
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cert-score">الدرجة (%)</Label>
                <Input
                  id="cert-score"
                  type="number"
                  min="0"
                  max="100"
                  value={manualCertificate.score}
                  onChange={(e) =>
                    setManualCertificate({
                      ...manualCertificate,
                      score: Number(e.target.value),
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cert-issue-date">تاريخ الإصدار</Label>
                <Input
                  id="cert-issue-date"
                  type="date"
                  value={manualCertificate.issueDate}
                  onChange={(e) =>
                    setManualCertificate({
                      ...manualCertificate,
                      issueDate: e.target.value,
                    })
                  }
                />
              </div>
            </div>

            <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-amber-800 dark:text-amber-200 mb-1">
                    تنبيه مهم
                  </h4>
                  <p className="text-sm text-amber-700 dark:text-amber-300">
                    سيتم إنشاء الشهادة مباشرة وإرسالها للطالب عبر البريد
                    الإلكتروني. تأكد من صحة البيانات قبل المتابعة.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowManualCertificateDialog(false)}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleCreateManualCertificate}
                disabled={
                  actionLoading === "create_certificate" ||
                  !manualCertificate.studentName ||
                  !manualCertificate.studentEmail
                }
                className="bg-amber-600 hover:bg-amber-700"
              >
                {actionLoading === "create_certificate" ? (
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                ) : (
                  <Award className="w-4 h-4 ml-2" />
                )}
                إنشاء الشهادة
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* QR Scanner Modal */}
      <Dialog open={qrScannerOpen} onOpenChange={setQrScannerOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>مسح QR Code</DialogTitle>
            <DialogDescription>
              وجه الكاميرا نحو QR Code الموجود على الشهادة
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center p-8">
            <div className="w-64 h-64 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <Camera className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-500 dark:text-slate-400">
                  كاميرا QR Scanner
                </p>
                <p className="text-sm text-slate-400 mt-2">
                  سيتم تفعيلها في النسخة النهائية
                </p>
                <Button
                  className="mt-4"
                  onClick={() => {
                    setCertificateVerification("CERT-2024-001234");
                    setQrScannerOpen(false);
                  }}
                >
                  محاكاة المسح
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Bulk Email Dialog */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>إرسال رسالة جماعية</DialogTitle>
            <DialogDescription>
              إرسال رسالة بريدية إلى {selectedUsers.length} مستخدم محدد
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email-subject">موضوع الرسالة</Label>
              <Input id="email-subject" placeholder="أدخل موضوع الرسالة" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email-template">محتوى الرسالة</Label>
              <Textarea
                id="email-template"
                placeholder="اكتب محتوى الرسالة هنا..."
                value={emailTemplate}
                onChange={(e) => setEmailTemplate(e.target.value)}
                rows={8}
              />
            </div>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowEmailDialog(false)}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleSendBulkEmail}
                disabled={
                  actionLoading === "send_email" || !emailTemplate.trim()
                }
                className="bg-blue-600 hover:bg-blue-700"
              >
                {actionLoading === "send_email" ? (
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                ) : (
                  <Send className="w-4 h-4 ml-2" />
                )}
                إرسال الرسالة
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Lecture Edit Dialog - Fixed positioning */}
      <Dialog
        open={!!editingLecture}
        onOpenChange={() => setEditingLecture(null)}
      >
        <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto fixed top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]">
          <DialogHeader>
            <DialogTitle>تعديل المحاضرة</DialogTitle>
            <DialogDescription>تعديل تفاصيل ومحتوى المحاضرة</DialogDescription>
          </DialogHeader>
          {editingLecture && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="lecture-title">عنوان المحاضرة (عربي)</Label>
                  <Input
                    id="lecture-title"
                    defaultValue={editingLecture.title}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lecture-title-en">
                    عنوان المحاضرة (إنجليزي)
                  </Label>
                  <Input
                    id="lecture-title-en"
                    defaultValue={editingLecture.titleEn}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lecture-duration">المدة (دقيقة)</Label>
                  <Input
                    id="lecture-duration"
                    type="number"
                    defaultValue={editingLecture.duration}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lecture-order">الترتيب</Label>
                  <Input
                    id="lecture-order"
                    type="number"
                    defaultValue={editingLecture.order}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="lecture-description">وصف المحاضرة</Label>
                <Textarea
                  id="lecture-description"
                  defaultValue={editingLecture.description}
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lecture-status">حالة النشر</Label>
                <Select defaultValue={editingLecture.status}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="published">منشور</SelectItem>
                    <SelectItem value="draft">مسودة</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>رفع الفيديو</Label>
                <div className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-8 text-center">
                  <FileVideo className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-500 dark:text-slate-400">
                    اسحب الفيديو هنا أو انقر للاختيار
                  </p>
                  <Button variant="outline" className="mt-4 bg-transparent">
                    <Upload className="w-4 h-4 ml-2" />
                    اختيار ملف
                  </Button>
                </div>
              </div>
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setEditingLecture(null)}
                >
                  إلغاء
                </Button>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Save className="w-4 h-4 ml-2" />
                  حفظ التغييرات
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Question Edit Dialog - Fixed positioning */}
      <Dialog
        open={!!editingQuestion}
        onOpenChange={() => setEditingQuestion(null)}
      >
        <DialogContent className="max-w-5xl max-h-[85vh] overflow-y-auto fixed top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]">
          <DialogHeader>
            <DialogTitle>إدارة الأسئلة</DialogTitle>
            <DialogDescription>
              {editingQuestion?.examType === "final"
                ? "إدارة أسئلة الاختبار النهائي"
                : "إدارة أسئلة اختبار المحاضرة"}
            </DialogDescription>
          </DialogHeader>
          {editingQuestion && (
            <div className="space-y-6">
              {/* Questions List */}
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {(editingQuestion.examType === "final"
                  ? mockAdminData.finalExam.questions
                  : editingQuestion.quiz?.questions || []
                ).map((question: any, index: number) => (
                  <div
                    key={question.id}
                    className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">سؤال {index + 1}</Badge>
                        {getDifficultyBadge(question.difficulty)}
                        {question.category && (
                          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                            {question.category}
                          </Badge>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Edit className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 bg-transparent"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="font-medium text-slate-900 dark:text-white mb-3">
                      {question.question}
                    </p>
                    <div className="space-y-2">
                      {question.options.map((option, optionIndex) => (
                        <div
                          key={optionIndex}
                          className={`text-sm p-2 rounded ${
                            optionIndex === question.correctAnswer
                              ? "bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300"
                              : "bg-slate-50 dark:bg-slate-800 text-slate-600 dark:text-slate-400"
                          }`}
                        >
                          {String.fromCharCode(65 + optionIndex)}. {option}
                          {optionIndex === question.correctAnswer && (
                            <CheckCircle className="w-4 h-4 inline mr-2 text-green-600" />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Add New Question Button */}
              <Button
                className="w-full bg-green-600 hover:bg-green-700"
                onClick={() =>
                  setNewQuestion({ examType: editingQuestion.examType })
                }
              >
                <Plus className="w-4 h-4 ml-2" />
                إضافة سؤال جديد
              </Button>

              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setEditingQuestion(null)}
                >
                  إغلاق
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
