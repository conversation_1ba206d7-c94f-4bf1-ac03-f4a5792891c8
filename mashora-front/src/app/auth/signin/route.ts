import { NextRequest, NextResponse } from "next/server";

// Mock user credentials for testing
const USER_CREDENTIALS = [
  {
    identifier: "<EMAIL>",
    password: "pepo123",
    user: {
      id: 1,
      firstName: "بيبو",
      lastName: "الطالب",
      email: "<EMAIL>",
      phone: "01012345678",
      avatar: "/placeholder.svg?height=120&width=120",
    },
  },
  {
    identifier: "01012345678",
    password: "pepo123",
    user: {
      id: 1,
      firstName: "بيبو",
      lastName: "الطالب",
      email: "<EMAIL>",
      phone: "01012345678",
      avatar: "/placeholder.svg?height=120&width=120",
    },
  },
  {
    identifier: "<EMAIL>",
    password: "student123",
    user: {
      id: 2,
      firstName: "الطالب",
      lastName: "النموذجي",
      email: "<EMAIL>",
      phone: "01098765432",
      avatar: "/placeholder.svg?height=120&width=120",
    },
  },
  {
    identifier: "<EMAIL>",
    password: "test123",
    user: {
      id: 3,
      firstName: "مستخدم",
      lastName: "تجريبي",
      email: "<EMAIL>",
      phone: "01555666777",
      avatar: "/placeholder.svg?height=120&width=120",
    },
  },
];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { identifier, password } = body;

    // Find matching credentials
    const credential = USER_CREDENTIALS.find(
      (cred) => cred.identifier === identifier && cred.password === password
    );

    if (credential) {
      // Generate mock token
      const token = `mock_user_token_${Date.now()}_${Math.random()}`;

      return NextResponse.json({
        success: true,
        token,
        user: credential.user,
        message: "تم تسجيل الدخول بنجاح",
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: "البريد الإلكتروني أو كلمة المرور غير صحيح",
        },
        { status: 401 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: "حدث خطأ في الخادم",
      },
      { status: 500 }
    );
  }
}
