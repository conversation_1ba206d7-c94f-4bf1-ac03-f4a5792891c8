"use client";

import type React from "react";

import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ModernNavbar } from "@/components/modern-navbar";
import { CalendarDate } from "@internationalized/date";

import {
  Eye,
  EyeOff,
  User,
  Mail,
  Phone,
  Lock,
  Calendar,
  MapPin,
  Church,
  Upload,
  X,
  ArrowLeft,
  Heart,
  Sparkles,
  AlertCircle,
  CheckCircle,
  Loader2,
  CreditCard,
  UserCheck,
  Zap,
  ArrowRight,
} from "lucide-react";
import Link from "next/link";
import { z } from "zod";
import { DateInput } from "@heroui/date-input";

// Validation schema
const signupSchema = z
  .object({
    firstName: z.string().min(2, { message: "الاسم الأول مطلوب" }).max(50),
    lastName: z.string().min(2, { message: "الاسم الأخير مطلوب" }).max(50),
    email: z.string().email({ message: "البريد الإلكتروني غير صالح" }),
    phone: z.string().regex(/^(\+20|0020|20)?(01[0-2]|015)[0-9]{8}$/, {
      message: "رقم الهاتف المصري غير صالح (مثال: 01208053304)",
    }),
    password: z
      .string()
      .min(8, { message: "كلمة المرور يجب أن تكون على الأقل 8 أحرف" }),
    confirmPassword: z.string(),
    nationalId: z.string().regex(/^[0-9]{14}$/, {
      message: "الرقم القومي يجب أن يتكون من 14 رقمًا",
    }),
    dateOfBirth: z.string().min(1, { message: "تاريخ الميلاد مطلوب" }),
    address: z.string().min(10, { message: "العنوان مطلوب" }).max(200),
    churchBelong: z.string().min(2, { message: "الكنيسة التابع لها مطلوبة" }),
    fatherOfConfession: z.string().min(2, { message: "أب الاعتراف مطلوب" }),
    confessionChurch: z
      .string()
      .min(2, { message: "كنيسة أب الاعتراف مطلوبة" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "كلمة المرور غير متطابقة",
    path: ["confirmPassword"],
  });

type SignupFormData = z.infer<typeof signupSchema>;

interface FileUploads {
  avatar?: File;
  nationalIdFront?: File;
  nationalIdBack?: File;
}

interface SavedSignupState {
  currentStep: number;
  formData: SignupFormData & { paymentMethod?: string };
  fileMetadata: {
    avatar?: { name: string; size: number; type: string };
    nationalIdFront?: { name: string; size: number; type: string };
    nationalIdBack?: { name: string; size: number; type: string };
  };
  timestamp: number;
}

// Constants for localStorage
const SIGNUP_STORAGE_KEY = "mashora-signup-progress";
const STORAGE_EXPIRY_DAYS = 7; // Data expires after 7 days

// Helper function to convert string date to CalendarDate
const stringToCalendarDate = (dateString: string): CalendarDate | null => {
  if (!dateString) return null;
  const [year, month, day] = dateString.split("-").map(Number);
  return new CalendarDate(year, month, day);
};

// Helper function to convert CalendarDate to string
const calendarDateToString = (date: CalendarDate | null): string => {
  if (!date) return "";
  const year = date.year;
  const month = String(date.month).padStart(2, "0");
  const day = String(date.day).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// LocalStorage utility functions
const saveSignupState = (
  currentStep: number,
  formData: SignupFormData & { paymentMethod?: string },
  fileUploads: FileUploads
) => {
  try {
    // Create file metadata (we can't store actual File objects in localStorage)
    const fileMetadata: SavedSignupState["fileMetadata"] = {};
    if (fileUploads.avatar) {
      fileMetadata.avatar = {
        name: fileUploads.avatar.name,
        size: fileUploads.avatar.size,
        type: fileUploads.avatar.type,
      };
    }
    if (fileUploads.nationalIdFront) {
      fileMetadata.nationalIdFront = {
        name: fileUploads.nationalIdFront.name,
        size: fileUploads.nationalIdFront.size,
        type: fileUploads.nationalIdFront.type,
      };
    }
    if (fileUploads.nationalIdBack) {
      fileMetadata.nationalIdBack = {
        name: fileUploads.nationalIdBack.name,
        size: fileUploads.nationalIdBack.size,
        type: fileUploads.nationalIdBack.type,
      };
    }

    const stateToSave: SavedSignupState = {
      currentStep,
      formData,
      fileMetadata,
      timestamp: Date.now(),
    };

    localStorage.setItem(SIGNUP_STORAGE_KEY, JSON.stringify(stateToSave));
    console.log("✅ Signup progress saved automatically");
  } catch (error) {
    console.error("❌ Failed to save signup progress:", error);
  }
};

const loadSignupState = (): SavedSignupState | null => {
  try {
    const savedState = localStorage.getItem(SIGNUP_STORAGE_KEY);
    if (!savedState) return null;

    const parsedState: SavedSignupState = JSON.parse(savedState);

    // Check if the saved state has expired
    const expiryTime = STORAGE_EXPIRY_DAYS * 24 * 60 * 60 * 1000; // Convert days to milliseconds
    const isExpired = Date.now() - parsedState.timestamp > expiryTime;

    if (isExpired) {
      console.log("🕐 Saved signup progress has expired, clearing data");
      localStorage.removeItem(SIGNUP_STORAGE_KEY);
      return null;
    }

    console.log("📁 Loading saved signup progress");
    return parsedState;
  } catch (error) {
    console.error("❌ Failed to load signup progress:", error);
    // Clear corrupted data
    localStorage.removeItem(SIGNUP_STORAGE_KEY);
    return null;
  }
};

const clearSignupState = () => {
  try {
    localStorage.removeItem(SIGNUP_STORAGE_KEY);
    console.log("🧹 Signup progress cleared");
  } catch (error) {
    console.error("❌ Failed to clear signup progress:", error);
  }
};

export default function SignupPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<
    SignupFormData & { paymentMethod?: string }
  >({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    nationalId: "",
    dateOfBirth: "",
    address: "",
    churchBelong: "",
    fatherOfConfession: "",
    confessionChurch: "",
    paymentMethod: "card",
  });
  const [fileUploads, setFileUploads] = useState<FileUploads>({});
  const [errors, setErrors] = useState<
    Partial<Record<keyof (SignupFormData & { paymentMethod?: string }), string>>
  >({});
  const [fileErrors, setFileErrors] = useState<
    Partial<Record<keyof FileUploads, string>>
  >({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [hasRestoredState, setHasRestoredState] = useState(false);
  const [showRestoredMessage, setShowRestoredMessage] = useState(false);

  const totalSteps = 5;

  // Load saved state on component mount
  useEffect(() => {
    const savedState = loadSignupState();
    if (savedState) {
      setCurrentStep(savedState.currentStep);
      setFormData(savedState.formData);
      setHasRestoredState(true);
      setShowRestoredMessage(true);

      // Hide the restored message after 5 seconds
      const timer = setTimeout(() => {
        setShowRestoredMessage(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, []);

  // Auto-save state whenever critical data changes
  const saveStateDebounced = useCallback(
    (
      step: number,
      data: SignupFormData & { paymentMethod?: string },
      files: FileUploads
    ) => {
      // Only save if we have meaningful data
      const hasData =
        data.firstName ||
        data.lastName ||
        data.email ||
        data.phone ||
        data.password ||
        data.nationalId ||
        data.address ||
        data.churchBelong ||
        Object.keys(files).some((key) => files[key as keyof FileUploads]);

      if (hasData) {
        // Debounce the save operation
        const timeoutId = setTimeout(() => {
          saveSignupState(step, data, files);
        }, 500);

        return () => clearTimeout(timeoutId);
      }
    },
    []
  );

  useEffect(() => {
    const cleanup = saveStateDebounced(currentStep, formData, fileUploads);
    return cleanup;
  }, [currentStep, formData, fileUploads, saveStateDebounced]);

  const handleInputChange = (
    field: keyof (SignupFormData & { paymentMethod?: string }),
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof SignupFormData]) {
      setErrors((prev) => ({
        ...prev,
        [field as keyof SignupFormData]: undefined,
      }));
    }
  };

  const handleFileChange = (field: keyof FileUploads, file: File | null) => {
    setFileUploads((prev) => ({ ...prev, [field]: file || undefined }));
    // Clear error when user selects a file
    if (file && fileErrors[field]) {
      setFileErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateCurrentStep = (): boolean => {
    const stepFields: Record<number, (keyof SignupFormData)[]> = {
      1: ["firstName", "lastName", "email", "phone"],
      2: ["password", "confirmPassword"],
      3: ["nationalId", "dateOfBirth", "address"],
      4: ["churchBelong", "fatherOfConfession", "confessionChurch"],
      5: [], // Payment step - file validation handled separately
    };

    const fieldsToValidate = stepFields[currentStep] || [];

    // Clear previous errors
    setErrors({});
    setFileErrors({});

    // Validate each field individually for better error handling
    const newErrors: Partial<Record<keyof SignupFormData, string>> = {};
    const newFileErrors: Partial<Record<keyof FileUploads, string>> = {};
    let isValid = true;

    // Special validation for step 5 - file uploads
    if (currentStep === 5) {
      if (!fileUploads.avatar) {
        newFileErrors.avatar = "الصورة الشخصية مطلوبة";
        isValid = false;
      }
      if (!fileUploads.nationalIdFront) {
        newFileErrors.nationalIdFront = "صورة البطاقة (الوجه) مطلوبة";
        isValid = false;
      }
      if (!fileUploads.nationalIdBack) {
        newFileErrors.nationalIdBack = "صورة البطاقة (الظهر) مطلوبة";
        isValid = false;
      }
    }

    fieldsToValidate.forEach((field) => {
      const value = formData[field];

      // Check if field is empty
      if (!value || value.trim() === "") {
        switch (field) {
          case "firstName":
            newErrors.firstName = "الاسم الأول مطلوب";
            break;
          case "lastName":
            newErrors.lastName = "الاسم الأخير مطلوب";
            break;
          case "email":
            newErrors.email = "البريد الإلكتروني مطلوب";
            break;
          case "phone":
            newErrors.phone = "رقم الهاتف مطلوب";
            break;
          case "password":
            newErrors.password = "كلمة المرور مطلوبة";
            break;
          case "confirmPassword":
            newErrors.confirmPassword = "تأكيد كلمة المرور مطلوب";
            break;
          case "nationalId":
            newErrors.nationalId = "الرقم القومي مطلوب";
            break;
          case "dateOfBirth":
            newErrors.dateOfBirth = "تاريخ الميلاد مطلوب";
            break;
          case "address":
            newErrors.address = "العنوان مطلوب";
            break;
          case "churchBelong":
            newErrors.churchBelong = "الكنيسة التابع لها مطلوبة";
            break;
          case "fatherOfConfession":
            newErrors.fatherOfConfession = "أب الاعتراف مطلوب";
            break;
          case "confessionChurch":
            newErrors.confessionChurch = "كنيسة أب الاعتراف مطلوبة";
            break;
        }
        isValid = false;
        return;
      }

      // Validate specific field formats
      switch (field) {
        case "firstName":
        case "lastName":
          if (value.length < 2) {
            newErrors[field] =
              field === "firstName"
                ? "الاسم الأول قصير جداً"
                : "الاسم الأخير قصير جداً";
            isValid = false;
          }
          break;

        case "email":
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            newErrors.email = "البريد الإلكتروني غير صالح";
            isValid = false;
          }
          break;

        case "phone":
          // Enhanced regex for Egyptian phone numbers
          // Supports: 01012345678, 01112345678, 01212345678, 01512345678
          // Also supports: +201012345678, 00201012345678, 201012345678
          const phoneRegex = /^(\+20|0020|20)?(01[0-2]|015)[0-9]{8}$/;
          if (!phoneRegex.test(value)) {
            newErrors.phone = "رقم الهاتف المصري غير صالح (مثال: 01208053304)";
            isValid = false;
          }
          break;

        case "password":
          if (value.length < 8) {
            newErrors.password = "كلمة المرور يجب أن تكون على الأقل 8 أحرف";
            isValid = false;
          }
          break;

        case "confirmPassword":
          if (value !== formData.password) {
            newErrors.confirmPassword = "كلمة المرور غير متطابقة";
            isValid = false;
          }
          break;

        case "nationalId":
          if (!/^[0-9]{14}$/.test(value)) {
            newErrors.nationalId = "الرقم القومي يجب أن يتكون من 14 رقمًا";
            isValid = false;
          }
          break;

        case "address":
          if (value.length < 10) {
            newErrors.address = "العنوان قصير جداً";
            isValid = false;
          }
          break;
      }
    });

    if (!isValid) {
      setErrors(newErrors);
      setFileErrors(newFileErrors);
    }

    console.log(
      "Validation result:",
      isValid,
      "Errors:",
      newErrors,
      "File Errors:",
      newFileErrors,
      "Form data:",
      formData
    );
    return isValid;
  };

  const nextStep = () => {
    console.log("nextStep called, current step:", currentStep);
    console.log("Current form data:", formData);

    const isValid = validateCurrentStep();
    console.log("Validation result:", isValid);

    if (isValid && currentStep < totalSteps) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      console.log("Moving to step:", newStep);
    } else {
      console.log("Validation failed or already at last step");
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateCurrentStep()) return;

    setIsLoading(true);
    setSubmitStatus("idle");
    setLoadingProgress(0);

    // Simulate loading progress
    const progressInterval = setInterval(() => {
      setLoadingProgress((prev) => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 100);

    try {
      const formDataToSend = new FormData();

      // Add text fields
      Object.entries(formData).forEach(([key, value]) => {
        formDataToSend.append(key, value);
      });

      // Add files
      Object.entries(fileUploads).forEach(([key, file]) => {
        if (file) {
          formDataToSend.append(key, file);
        }
      });

      const response = await fetch("/auth/signup", {
        method: "POST",
        body: formDataToSend,
      });

      clearInterval(progressInterval);
      setLoadingProgress(100);

      if (response.ok) {
        setSubmitStatus("success");
        // Clear saved progress on successful registration
        clearSignupState();
        setTimeout(() => {
          window.location.href = "/signin";
        }, 2000);
      } else {
        setSubmitStatus("error");
      }
    } catch (error) {
      console.error("Registration failed:", error);
      clearInterval(progressInterval);
      setLoadingProgress(0);
      setSubmitStatus("error");
    } finally {
      setTimeout(() => {
        setIsLoading(false);
        setLoadingProgress(0);
      }, 500);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl shadow-lg">
                  <User className="w-8 h-8 text-white" />
                </div>
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">
                البيانات الشخصية
              </h3>
              <p className="text-slate-500 dark:text-slate-400 text-lg">
                Personal Information
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label
                  htmlFor="firstName"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <User className="w-5 h-5 text-amber-500" />
                  الاسم الأول
                </Label>
                <div className="relative group">
                  <Input
                    id="firstName"
                    type="text"
                    placeholder="أبانوب"
                    value={formData.firstName}
                    onChange={(e) =>
                      handleInputChange("firstName", e.target.value)
                    }
                    disabled={isLoading}
                    className={`text-right text-lg py-4 px-6 border-2 transition-all duration-300 ${
                      errors.firstName
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg`}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.firstName && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.firstName}
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <Label
                  htmlFor="lastName"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <User className="w-5 h-5 text-amber-500" />
                  الاسم الأخير
                </Label>
                <div className="relative group">
                  <Input
                    id="lastName"
                    type="text"
                    placeholder="نشأت"
                    value={formData.lastName}
                    onChange={(e) =>
                      handleInputChange("lastName", e.target.value)
                    }
                    disabled={isLoading}
                    className={`text-right text-lg py-4 px-6 border-2 transition-all duration-300 ${
                      errors.lastName
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg`}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.lastName && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.lastName}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-3">
              <Label
                htmlFor="email"
                className="text-foreground font-semibold flex items-center gap-2 text-lg"
              >
                <Mail className="w-5 h-5 text-amber-500" />
                البريد الإلكتروني
              </Label>
              <div className="relative group">
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  disabled={isLoading}
                  className={`text-right text-lg py-4 px-6 border-2 transition-all duration-300 ${
                    errors.email
                      ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                      : isLoading
                      ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                      : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                  } rounded-xl shadow-sm focus:shadow-lg`}
                  dir="ltr"
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                {isLoading && (
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                    <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                  </div>
                )}
              </div>
              {errors.email && (
                <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                  <AlertCircle className="w-4 h-4 flex-shrink-0" />
                  {errors.email}
                </div>
              )}
            </div>

            <div className="space-y-3">
              <Label
                htmlFor="phone"
                className="text-foreground font-semibold flex items-center gap-2 text-lg"
              >
                <Phone className="w-5 h-5 text-amber-500" />
                رقم الهاتف
              </Label>
              <div className="relative group">
                <Input
                  id="phone"
                  type="tel"
                  placeholder="01012345678"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  disabled={isLoading}
                  className={`text-right text-lg py-4 px-6 border-2 transition-all duration-300 ${
                    errors.phone
                      ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                      : isLoading
                      ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                      : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                  } rounded-xl shadow-sm focus:shadow-lg`}
                  dir="ltr"
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                {isLoading && (
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                    <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                  </div>
                )}
              </div>
              {errors.phone && (
                <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                  <AlertCircle className="w-4 h-4 flex-shrink-0" />
                  {errors.phone}
                </div>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl shadow-lg">
                  <Lock className="w-8 h-8 text-white" />
                </div>
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">
                كلمة المرور
              </h3>
              <p className="text-slate-500 dark:text-slate-400 text-lg">
                Password Setup
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-3">
                <Label
                  htmlFor="password"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <Lock className="w-5 h-5 text-amber-500" />
                  كلمة المرور
                </Label>
                <div className="relative group">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="أدخل كلمة مرور قوية"
                    value={formData.password}
                    onChange={(e) =>
                      handleInputChange("password", e.target.value)
                    }
                    disabled={isLoading}
                    className={`text-right text-lg py-4 px-6 pl-12 border-2 transition-all duration-300 ${
                      errors.password
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg`}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-amber-400 transition-colors disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.password && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.password}
                  </div>
                )}
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  يجب أن تحتوي على 8 أحرف على الأقل
                </p>
              </div>

              <div className="space-y-3">
                <Label
                  htmlFor="confirmPassword"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <Lock className="w-5 h-5 text-amber-500" />
                  تأكيد كلمة المرور
                </Label>
                <div className="relative group">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="أعد إدخال كلمة المرور"
                    value={formData.confirmPassword}
                    onChange={(e) =>
                      handleInputChange("confirmPassword", e.target.value)
                    }
                    disabled={isLoading}
                    className={`text-right text-lg py-4 px-6 pl-12 border-2 transition-all duration-300 ${
                      errors.confirmPassword
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg`}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={isLoading}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-amber-400 transition-colors disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.confirmPassword && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.confirmPassword}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl shadow-lg">
                  <CreditCard className="w-8 h-8 text-white" />
                </div>
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">
                بيانات الهوية
              </h3>
              <p className="text-slate-500 dark:text-slate-400 text-lg">
                Identity Information
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-3">
                <Label
                  htmlFor="nationalId"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <CreditCard className="w-5 h-5 text-amber-500" />
                  الرقم القومي
                </Label>
                <div className="relative group">
                  <Input
                    id="nationalId"
                    type="text"
                    placeholder="12345678901234"
                    maxLength={14}
                    value={formData.nationalId}
                    onChange={(e) =>
                      handleInputChange(
                        "nationalId",
                        e.target.value.replace(/\D/g, "")
                      )
                    }
                    disabled={isLoading}
                    className={`text-right text-lg py-4 px-6 border-2 transition-all duration-300 ${
                      errors.nationalId
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg`}
                    dir="ltr"
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.nationalId && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.nationalId}
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <Label
                  htmlFor="dateOfBirth"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <Calendar className="w-5 h-5 text-amber-500" />
                  تاريخ الميلاد
                </Label>
                <div className="relative group">
                  <DateInput
                    value={stringToCalendarDate(formData.dateOfBirth)}
                    onChange={(date) => {
                      const dateString = calendarDateToString(date);
                      handleInputChange("dateOfBirth", dateString);
                    }}
                    isDisabled={isLoading}
                    isInvalid={!!errors.dateOfBirth}
                    errorMessage={errors.dateOfBirth}
                    labelPlacement="outside"
                    maxValue={
                      new CalendarDate(
                        new Date().getFullYear(),
                        new Date().getMonth() + 1,
                        new Date().getDate()
                      )
                    }
                    minValue={new CalendarDate(1900, 1, 1)}
                    className={`text-right text-lg px-3 border-2 transition-all duration-300 ${
                      errors.dateOfBirth
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg`}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <Label
                  htmlFor="address"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <MapPin className="w-5 h-5 text-amber-500" />
                  العنوان
                </Label>
                <div className="relative group">
                  <Textarea
                    id="address"
                    placeholder="العنوان بالتفصيل"
                    value={formData.address}
                    onChange={(e) =>
                      handleInputChange("address", e.target.value)
                    }
                    disabled={isLoading}
                    className={`text-right text-lg py-4 px-6 min-h-[120px] border-2 transition-all duration-300 ${
                      errors.address
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg resize-none`}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  {isLoading && (
                    <div className="absolute right-4 top-4">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.address && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.address}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl shadow-lg">
                  <Church className="w-8 h-8 text-white" />
                </div>
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">
                البيانات الكنسية
              </h3>
              <p className="text-slate-500 dark:text-slate-400 text-lg">
                Church Information
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-3">
                <Label
                  htmlFor="churchBelong"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <Church className="w-5 h-5 text-amber-500" />
                  الكنيسة التابع لها
                </Label>
                <div className="relative group">
                  <select
                    id="churchBelong"
                    value={formData.churchBelong}
                    onChange={(e) =>
                      handleInputChange("churchBelong", e.target.value)
                    }
                    disabled={isLoading}
                    className={`w-full text-right text-lg px-6 border-2 transition-all duration-300 ${
                      errors.churchBelong
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg bg-white dark:bg-slate-800`}
                  >
                    <option value="">اختر الكنيسة</option>
                    <option value="السيدة العذراء والقديس يوسف النجار - الخصوص">
                      السيدة العذراء والقديس يوسف النجار - الخصوص
                    </option>
                    <option value="السيدة العذراء والرسولي بطرس وبولس - الخصوص">
                      السيدة العذراء والرسولي بطرس وبولس - الخصوص
                    </option>
                    <option value="البابا أثناسيوس الرسول والانبا بيشوي - الخصوص">
                      البابا أثناسيوس الرسول والانبا بيشوي - الخصوص
                    </option>
                    <option value="السيدة العذراء والقديس ابى سيفين - الخصوص">
                      السيدة العذراء والقديس ابى سيفين - الخصوص
                    </option>
                    <option value="الانبا كاراس والانبا ابرام - الخصوص">
                      الانبا كاراس والانبا ابرام - الخصوص
                    </option>
                    <option value="السيدة العذراء والشهيد العظيم أبانوب - الخصوص">
                      السيدة العذراء والشهيد العظيم أبانوب - الخصوص
                    </option>
                    <option value="السيدة العذراء والانبا موسى - الخصوص">
                      السيدة العذراء والانبا موسى - الخصوص
                    </option>
                    <option value="السيدة العذراء والملاك ميخائيل - الخصوص">
                      السيدة العذراء والملاك ميخائيل - الخصوص
                    </option>
                    <option value="الشهيد العظيم مارمينا والبابا كيرلس السادس - الخصوص">
                      الشهيد العظيم مارمينا والبابا كيرلس السادس - الخصوص
                    </option>
                    <option value="الشهيد العظيم مارجرجس والبابا ديسقوروس - الخصوص">
                      الشهيد العظيم مارجرجس والبابا ديسقوروس - الخصوص
                    </option>
                    <option value="السيدة العذراء والقديس ماريوحنا الحبيب - الخصوص">
                      السيدة العذراء والقديس ماريوحنا الحبيب - الخصوص
                    </option>
                    <option value="الشهيد العظيم مارجرجس - قها">
                      الشهيد العظيم مارجرجس - قها
                    </option>
                    <option value="الشهيد العظيم مارجرجس - طوخ">
                      الشهيد العظيم مارجرجس - طوخ
                    </option>
                    <option value="القديسة الشهيدة دميانة - ميت كنانه">
                      القديسة الشهيدة دميانة - ميت كنانه
                    </option>
                    <option value="الشهيد العظيم مارجرجس - بلتان">
                      الشهيد العظيم مارجرجس - بلتان
                    </option>
                    <option value="الشهيد العظيم مارمينا العجايبي - ساحل دجوي">
                      الشهيد العظيم مارمينا العجايبي - ساحل دجوي
                    </option>
                    <option value="السيدة العذراء والقديس العظيم ابى سيفين - ساحل دجوى">
                      السيدة العذراء والقديس العظيم ابى سيفين - ساحل دجوى
                    </option>
                    <option value="رئيس الملائكة الجليل ميخائيل - القلزم">
                      رئيس الملائكة الجليل ميخائيل - القلزم
                    </option>
                    <option value="السيدة العذراء والقديس مارمرقس الرسول - كفر شبين">
                      السيدة العذراء والقديس مارمرقس الرسول - كفر شبين
                    </option>
                    <option value="الشهيد العظيم مارجرجس - منيه شبين">
                      الشهيد العظيم مارجرجس - منيه شبين
                    </option>
                    <option value="البابا كيرلس السادس - الحصافة">
                      البابا كيرلس السادس - الحصافة
                    </option>
                    <option value="رئيس الملائكة الجليل ميخائيل - القشيش">
                      رئيس الملائكة الجليل ميخائيل - القشيش
                    </option>
                    <option value="السيدة العذراء والقديس ابى سيفين - السلمانية">
                      السيدة العذراء والقديس ابى سيفين - السلمانية
                    </option>
                    <option value="الشهيد العظيم مارجرجس والانبا كاراس - نوى">
                      الشهيد العظيم مارجرجس والانبا كاراس - نوى
                    </option>
                    <option value="السيدة العذراء - مساكن ابو زعبل">
                      السيدة العذراء - مساكن ابو زعبل
                    </option>
                    <option value="الشهيد العظيم مارجرجس - ابو زعبل">
                      الشهيد العظيم مارجرجس - ابو زعبل
                    </option>
                    <option value="رئيس الملائكة الجليل ميخائيل - العكرشة">
                      رئيس الملائكة الجليل ميخائيل - العكرشة
                    </option>
                    <option value="البابا بطرس خاتم الشهداء - الخانكة">
                      البابا بطرس خاتم الشهداء - الخانكة
                    </option>
                    <option value="الشهيد العظيم مارمينا والبابا كيرلس السادس - الجبل الاصفر">
                      الشهيد العظيم مارمينا والبابا كيرلس السادس - الجبل الاصفر
                    </option>
                    <option value="السيدة العذراء والقديس ابانوب - الفلج">
                      السيدة العذراء والقديس ابانوب - الفلج
                    </option>
                    <option value="الشهيد العظيم ابى سيفين والقديسة دميانة - الفلج">
                      الشهيد العظيم ابى سيفين والقديسة دميانة - الفلج
                    </option>
                    <option value="السيدة العذراء والامير تادرس - الفلج">
                      السيدة العذراء والامير تادرس - الفلج
                    </option>
                    <option value="السيدة العذراء والانبا بيشوي - المنيا">
                      السيدة العذراء والانبا بيشوي - المنيا
                    </option>
                    <option value="مديح الامين - ارض عبقة - الخصوص">
                      مديح الامين - ارض عبقة - الخصوص
                    </option>
                  </select>
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.churchBelong && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.churchBelong}
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <Label
                  htmlFor="fatherOfConfession"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <UserCheck className="w-5 h-5 text-amber-500" />
                  أب الاعتراف
                </Label>
                <div className="relative group">
                  <Input
                    id="fatherOfConfession"
                    type="text"
                    placeholder="أبونا يوسف"
                    value={formData.fatherOfConfession}
                    onChange={(e) =>
                      handleInputChange("fatherOfConfession", e.target.value)
                    }
                    disabled={isLoading}
                    className={`text-right text-lg py-4 px-6 border-2 transition-all duration-300 ${
                      errors.fatherOfConfession
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg`}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.fatherOfConfession && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.fatherOfConfession}
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <Label
                  htmlFor="confessionChurch"
                  className="text-foreground font-semibold flex items-center gap-2 text-lg"
                >
                  <Church className="w-5 h-5 text-amber-500" />
                  كنيسة أب الاعتراف
                </Label>
                <div className="relative group">
                  <Input
                    id="confessionChurch"
                    type="text"
                    placeholder="كنيسة مارجرجس"
                    value={formData.confessionChurch}
                    onChange={(e) =>
                      handleInputChange("confessionChurch", e.target.value)
                    }
                    disabled={isLoading}
                    className={`text-right text-lg py-4 px-6 border-2 transition-all duration-300 ${
                      errors.confessionChurch
                        ? "border-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/20"
                        : isLoading
                        ? "border-slate-300 dark:border-slate-600 bg-slate-100 dark:bg-slate-800/50 cursor-not-allowed"
                        : "border-slate-200 dark:border-slate-700 focus:border-amber-500 bg-white dark:bg-slate-800 hover:border-amber-400"
                    } rounded-xl shadow-sm focus:shadow-lg`}
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/0 to-amber-500/0 group-hover:from-amber-500/5 group-hover:via-amber-500/10 group-hover:to-amber-500/5 transition-all duration-300 pointer-events-none" />
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-slate-400 animate-spin" />
                    </div>
                  )}
                </div>
                {errors.confessionChurch && (
                  <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    {errors.confessionChurch}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-foreground mb-2">
                الدفع والملفات
              </h3>
              <p className="text-slate-400">Payment & Files</p>
            </div>

            {/* Payment Section */}
            <div className="space-y-4 p-6 glass-card rounded-xl border border-slate-700/50">
              <h4 className="font-semibold text-foreground flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-amber-400" />
                طريقة الدفع - 200 جنيه
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                    formData.paymentMethod === "card"
                      ? "border-amber-500 bg-amber-50 dark:bg-amber-900/20"
                      : "border-slate-300 dark:border-slate-600 hover:border-amber-400"
                  }`}
                  onClick={() => handleInputChange("paymentMethod", "card")}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        formData.paymentMethod === "card"
                          ? "border-amber-500 bg-amber-500"
                          : "border-slate-400"
                      }`}
                    >
                      {formData.paymentMethod === "card" && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                      )}
                    </div>
                    <div>
                      <h5 className="font-semibold text-foreground">
                        بطاقة ائتمان
                      </h5>
                      <p className="text-sm text-slate-400">Credit Card</p>
                    </div>
                  </div>
                </div>

                <div
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                    formData.paymentMethod === "cash"
                      ? "border-amber-500 bg-amber-50 dark:bg-amber-900/20"
                      : "border-slate-300 dark:border-slate-600 hover:border-amber-400"
                  }`}
                  onClick={() => handleInputChange("paymentMethod", "cash")}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        formData.paymentMethod === "cash"
                          ? "border-amber-500 bg-amber-500"
                          : "border-slate-400"
                      }`}
                    >
                      {formData.paymentMethod === "cash" && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                      )}
                    </div>
                    <div>
                      <h5 className="font-semibold text-foreground">
                        دفع نقدي
                      </h5>
                      <p className="text-sm text-slate-400">Cash Payment</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-800 dark:text-blue-300">
                  💡 يمكنك الدفع نقدياً في مكتب المطرانية أو عبر البطاقة
                  الائتمانية الآن
                </p>
              </div>
            </div>

            {/* File Uploads Section */}
            <div className="space-y-4 p-6 glass-card rounded-xl border border-slate-700/50">
              <h4 className="font-semibold text-foreground flex items-center gap-2">
                <Upload className="w-5 h-5 text-amber-400" />
                الملفات المطلوبة
              </h4>

              <div className="grid grid-cols-1 gap-4">
                <FileUploadField
                  label="الصورة الشخصية"
                  file={fileUploads.avatar}
                  onChange={(file) => handleFileChange("avatar", file)}
                  error={fileErrors.avatar}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FileUploadField
                    label="صورة البطاقة (الوجه)"
                    file={fileUploads.nationalIdFront}
                    onChange={(file) =>
                      handleFileChange("nationalIdFront", file)
                    }
                    error={fileErrors.nationalIdFront}
                  />

                  <FileUploadField
                    label="صورة البطاقة (الظهر)"
                    file={fileUploads.nationalIdBack}
                    onChange={(file) =>
                      handleFileChange("nationalIdBack", file)
                    }
                    error={fileErrors.nationalIdBack}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className="min-h-screen bg-background text-foreground font-cairo overflow-x-hidden transition-colors duration-300"
      dir="rtl"
    >
      {/* Enhanced Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-amber-50/30 to-orange-50/50 dark:from-slate-950 dark:via-slate-900/80 dark:to-slate-950 transition-all duration-700" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(251,191,36,0.15),transparent_40%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(251,191,36,0.08),transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.1),transparent_40%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.05),transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_90%_10%,rgba(236,72,153,0.08),transparent_40%)] dark:bg-[radial-gradient(circle_at_90%_10%,rgba(236,72,153,0.04),transparent_40%)]" />
      </div>

      {/* Modern Navbar */}
      <ModernNavbar variant="minimal" />

      {/* Main Content */}
      <main className="relative z-10 py-8 lg:py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            {/* Enhanced Page Header */}
            <div className="text-center mb-10 animate-fade-in-up">
              <div className="inline-flex items-center gap-2 mb-6">
                <Badge className="bg-gradient-to-r from-amber-500/20 via-orange-500/20 to-red-500/20 text-amber-700 dark:text-amber-300 hover:from-amber-500/30 hover:via-orange-500/30 hover:to-red-500/30 text-sm px-6 py-3 border border-amber-500/30 backdrop-blur-sm transition-all duration-300 hover:scale-105">
                  <Heart className="w-4 h-4 ml-2 animate-pulse" />
                  إنشاء حساب جديد • Sign Up
                </Badge>
              </div>

              <div className="space-y-4">
                <h1 className="text-5xl lg:text-6xl font-black text-foreground">
                  <span className="bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent animate-gradient">
                    انضم إلينا
                  </span>
                </h1>
                <h2 className="text-2xl font-semibold text-slate-600 dark:text-slate-400">
                  Join Us
                </h2>
                <p className="text-slate-500 dark:text-slate-400 text-lg max-w-md mx-auto leading-relaxed">
                  أنشئ حسابك لبدء كورس الإعداد للزواج
                </p>
              </div>
            </div>

            {/* Enhanced Progress Steps */}
            <div className="mb-10 animate-scale-in">
              <div className="flex items-center justify-center mb-6">
                {Array.from({ length: totalSteps }, (_, i) => (
                  <div key={i} className="flex items-center">
                    <div
                      className={`relative w-14 h-14 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-500 shadow-lg ${
                        i + 1 < currentStep
                          ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white"
                          : i + 1 === currentStep
                          ? "bg-gradient-to-r from-amber-500 to-orange-500 text-white animate-pulse"
                          : "bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-400"
                      }`}
                    >
                      {i + 1 < currentStep ? (
                        <CheckCircle className="w-7 h-7" />
                      ) : (
                        i + 1
                      )}
                      {i + 1 === currentStep && (
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 animate-ping opacity-20"></div>
                      )}
                    </div>
                    {i < totalSteps - 1 && (
                      <div
                        className={`w-24 h-2 mx-4 rounded-full transition-all duration-500 ${
                          i + 1 < currentStep
                            ? "bg-gradient-to-r from-green-500 to-emerald-500"
                            : i + 1 === currentStep
                            ? "bg-gradient-to-r from-amber-500/50 to-orange-500/50"
                            : "bg-slate-200 dark:bg-slate-700"
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>
              <div className="text-center">
                <div className="inline-flex items-center gap-3 px-6 py-3 rounded-full glass-card border border-amber-500/20">
                  <div className="w-3 h-3 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 animate-pulse"></div>
                  <span className="text-sm font-semibold text-foreground">
                    الخطوة {currentStep} من {totalSteps}
                  </span>
                  <span className="text-xs text-slate-500 dark:text-slate-400">
                    {currentStep === 1 && "البيانات الشخصية"}
                    {currentStep === 2 && "كلمة المرور"}
                    {currentStep === 3 && "بيانات الهوية"}
                    {currentStep === 4 && "البيانات الكنسية"}
                    {currentStep === 5 && "الدفع والملفات"}
                  </span>
                  {/* Auto-save indicator */}
                  <div className="flex items-center gap-1 mt-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                      حفظ تلقائي
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Restored State Message */}
            {showRestoredMessage && hasRestoredState && (
              <div className="mb-6 animate-fade-in">
                <div className="flex items-center gap-3 text-blue-700 dark:text-blue-300 text-sm bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800 shadow-sm">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-5 h-5 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold mb-1">
                      تم استعادة تقدمك السابق! ✨
                    </div>
                    <div className="text-blue-600 dark:text-blue-400 text-xs">
                      يمكنك المتابعة من حيث توقفت • Progress automatically
                      restored
                    </div>
                  </div>
                  <button
                    onClick={() => setShowRestoredMessage(false)}
                    className="text-blue-400 hover:text-blue-600 transition-colors"
                    aria-label="إغلاق الرسالة"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>
            )}

            {/* Enhanced Signup Form */}
            <Card className="relative overflow-hidden border-0 shadow-2xl bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl animate-scale-in">
              {/* Decorative Elements */}
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-amber-500 via-orange-500 to-red-500" />

              <CardContent className="p-8">
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    if (currentStep === totalSteps) {
                      handleSubmit(e);
                    } else {
                      nextStep();
                    }
                  }}
                >
                  {renderStepContent()}

                  {/* Enhanced Submit Status */}
                  {submitStatus === "success" && (
                    <div className="flex items-center gap-3 text-green-700 dark:text-green-300 text-sm bg-green-50 dark:bg-green-900/20 p-4 rounded-xl border border-green-200 dark:border-green-800 animate-fade-in mt-6">
                      <CheckCircle className="w-5 h-5 flex-shrink-0" />
                      <div>
                        <div className="font-semibold">
                          تم إنشاء الحساب بنجاح!
                        </div>
                        <div className="text-green-600 dark:text-green-400">
                          جاري التوجيه لصفحة تسجيل الدخول...
                        </div>
                      </div>
                    </div>
                  )}

                  {submitStatus === "error" && (
                    <div className="flex items-center gap-3 text-red-700 dark:text-red-300 text-sm bg-red-50 dark:bg-red-900/20 p-4 rounded-xl border border-red-200 dark:border-red-800 animate-fade-in mt-6">
                      <AlertCircle className="w-5 h-5 flex-shrink-0" />
                      <div>
                        <div className="font-semibold">
                          حدث خطأ أثناء إنشاء الحساب
                        </div>
                        <div className="text-red-600 dark:text-red-400">
                          يرجى المحاولة مرة أخرى
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Enhanced Navigation Buttons */}
                  <div className="space-y-6">
                    {/* Progress Bar */}
                    {isLoading && (
                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 rounded-full transition-all duration-300 ease-out"
                          style={{ width: `${loadingProgress}%` }}
                        />
                      </div>
                    )}

                    <div className="flex justify-between items-center pt-6 border-t-2 border-slate-200 dark:border-slate-700">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={prevStep}
                        disabled={currentStep === 1 || isLoading}
                        className={`border-2 text-lg font-semibold px-8 py-4 rounded-2xl transition-all duration-300 ${
                          isLoading
                            ? "border-slate-300 dark:border-slate-600 text-slate-400 dark:text-slate-500 bg-slate-100 dark:bg-slate-800/30 cursor-not-allowed"
                            : "border-slate-200 dark:border-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-50 dark:hover:bg-amber-900/20 hover:border-amber-400 dark:hover:border-amber-400 bg-white/50 dark:bg-slate-800/50 hover:scale-[1.02]"
                        }`}
                      >
                        <ArrowRight className="w-5 h-5 ml-3" />
                        السابق
                      </Button>

                      <Button
                        type="submit"
                        disabled={isLoading}
                        className="bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 hover:from-amber-600 hover:via-orange-600 hover:to-red-600 text-white text-xl font-bold px-10 py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none relative overflow-hidden group"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
                        {isLoading ? (
                          <div className="flex items-center gap-3">
                            <Loader2 className="w-6 h-6 animate-spin" />
                            جاري الإنشاء...
                          </div>
                        ) : currentStep === totalSteps ? (
                          <div className="flex items-center gap-3">
                            <span>إنشاء الحساب</span>
                            <Sparkles className="w-6 h-6 group-hover:animate-pulse" />
                          </div>
                        ) : (
                          <div className="flex items-center gap-3">
                            <span>التالي</span>
                            <ArrowRight className="w-6 h-6 rotate-180 group-hover:translate-x-1 transition-transform duration-300" />
                          </div>
                        )}
                      </Button>
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Enhanced Signin Link */}
            <div className="text-center mt-10 animate-fade-in-up">
              <p className="text-slate-600 dark:text-slate-400 text-lg mb-2">
                لديك حساب بالفعل؟
              </p>
              <Link
                href="/signin"
                className={`inline-flex items-center gap-3 font-semibold transition-all duration-300 group ${
                  isLoading
                    ? "text-slate-400 cursor-not-allowed pointer-events-none"
                    : "text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 hover:gap-4"
                }`}
              >
                <Zap
                  className={`w-5 h-5 ${
                    !isLoading && "group-hover:animate-pulse"
                  }`}
                />
                تسجيل الدخول
              </Link>
            </div>

            {/* Enhanced Back to Home */}
            <div className="text-center mt-6 animate-fade-in-up">
              <Link
                href="/"
                className={`inline-flex items-center gap-3 transition-all duration-300 group ${
                  isLoading
                    ? "text-slate-400 cursor-not-allowed pointer-events-none"
                    : "text-slate-500 dark:text-slate-400 hover:text-amber-500 dark:hover:text-amber-400 hover:gap-4"
                }`}
              >
                <span className="font-medium">العودة للصفحة الرئيسية</span>
                <ArrowLeft
                  className={`w-5 h-5 ${
                    !isLoading &&
                    "group-hover:translate-x-1 transition-transform duration-300"
                  }`}
                />
              </Link>
            </div>

            {/* Clear Saved Progress Option */}
            {hasRestoredState && (
              <div className="text-center mt-4 animate-fade-in-up">
                <button
                  onClick={() => {
                    if (
                      confirm(
                        "هل أنت متأكد من حذف التقدم المحفوظ؟ سيتم البدء من جديد."
                      )
                    ) {
                      clearSignupState();
                      window.location.reload();
                    }
                  }}
                  disabled={isLoading}
                  className={`text-xs transition-all duration-300 ${
                    isLoading
                      ? "text-slate-400 cursor-not-allowed"
                      : "text-slate-400 hover:text-red-500 dark:hover:text-red-400"
                  }`}
                >
                  🗑️ حذف التقدم المحفوظ والبدء من جديد
                </button>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Enhanced Floating Elements */}
      <div className="absolute top-32 right-16 w-24 h-24 bg-gradient-to-br from-amber-400/20 to-orange-400/20 rounded-full animate-float blur-sm" />
      <div
        className="absolute bottom-32 left-16 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full animate-float blur-sm"
        style={{ animationDelay: "2s" }}
      />
      <div
        className="absolute top-1/2 right-1/4 w-16 h-16 bg-gradient-to-br from-pink-400/20 to-red-400/20 rounded-full animate-float blur-sm"
        style={{ animationDelay: "4s" }}
      />
    </div>
  );
}

// File Upload Component
function FileUploadField({
  label,
  file,
  onChange,
  error,
}: {
  label: string;
  file?: File;
  onChange: (file: File | null) => void;
  error?: string;
}) {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    onChange(selectedFile);
  };

  const removeFile = () => {
    onChange(null);
  };

  return (
    <div className="space-y-2">
      <Label className="text-foreground font-semibold flex items-center gap-2">
        <Upload className="w-4 h-4 text-amber-400" />
        {label}
      </Label>

      {!file ? (
        <div
          className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            error
              ? "border-red-500 hover:border-red-400 bg-red-50 dark:bg-red-900/20"
              : "border-slate-600 hover:border-amber-400"
          }`}
        >
          <Upload
            className={`w-8 h-8 mx-auto mb-2 ${
              error ? "text-red-400" : "text-amber-400"
            }`}
          />
          <p className="text-slate-400 mb-2">اضغط لاختيار الملف</p>
          <input
            type="file"
            accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
            onChange={handleFileChange}
            className="absolute w-full h-full opacity-0 top-0 left-0 cursor-pointer"
          />
          <p className="text-xs text-slate-500">
            JPEG, PNG, WebP (حد أقصى 5MB)
          </p>
        </div>
      ) : (
        <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
          <span className="text-sm text-foreground truncate">{file.name}</span>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={removeFile}
            className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}

      {error && (
        <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 animate-shake">
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          {error}
        </div>
      )}
    </div>
  );
}
