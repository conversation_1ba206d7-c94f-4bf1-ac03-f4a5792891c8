"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import {
  Home,
  ArrowRight,
  Menu,
  X,
  User,
  LogOut,
  BookOpen,
  Award,
  Phone,
  Mail,
  MessageSquare,
  Users,
  Sparkles,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useScroll } from "@/hooks/use-scroll";
import { Button } from "@/components/ui/button";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface NavItem {
  href: string;
  label: string;
  icon?: React.ReactNode;
  isActive?: boolean;
}

interface ModernNavbarProps {
  variant?: "default" | "minimal" | "dashboard";
  showBackButton?: boolean;
  backHref?: string;
  backLabel?: string;
  navItems?: NavItem[];
  showUserMenu?: boolean;
  userInfo?: {
    name: string;
    email?: string;
    avatar?: string;
  };
  className?: string;
}

export function ModernNavbar({
  variant = "default",
  showBackButton = false,
  backHref = "/",
  backLabel = "العودة",
  navItems = [],
  showUserMenu = false,
  userInfo,
  className,
}: ModernNavbarProps) {
  const pathname = usePathname();
  const { scrollY, isScrolled, isScrolledPast, isMounted } = useScroll();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

  // Calculate dynamic styles based on scroll - only after mount to prevent hydration issues
  const navbarOpacity = isMounted && isScrolled ? 0.95 : 1;
  const navbarScale = isMounted && isScrolledPast(100) ? 0.98 : 1;
  const shadowIntensity = isMounted ? Math.min(scrollY / 100, 1) : 0;

  const defaultNavItems: NavItem[] = [
    {
      href: "/",
      label: "الرئيسية",
      icon: <Home className="w-4 h-4" />,
      isActive: pathname === "/",
    },
    {
      href: "#about",
      label: "عن الكورس",
      icon: <BookOpen className="w-4 h-4" />,
      isActive: false,
    },
    {
      href: "#features",
      label: "المميزات",
      icon: <Sparkles className="w-4 h-4" />,
      isActive: false,
    },
    {
      href: "#testimonials",
      label: "التقييمات",
      icon: <MessageSquare className="w-4 h-4" />,
      isActive: false,
    },
    {
      href: "#contact",
      label: "تواصل معنا",
      icon: <Mail className="w-4 h-4" />,
      isActive: false,
    },
  ];

  const currentNavItems = navItems.length > 0 ? navItems : defaultNavItems;

  return (
    <header
      className={cn(
        // Enhanced sticky positioning with highest z-index
        "sticky-navbar sticky top-0 left-0 right-0 w-full z-[100] transition-all duration-300 ease-out",
        "will-change-transform transform-gpu", // Optimize for transform animations
        // Ensure sticky behavior works across all browsers
        "supports-[position:sticky]:sticky",
        // Dynamic glass morphism based on scroll state - only after mount
        isMounted && isScrolled
          ? "bg-white/40 dark:bg-slate-900/40" // More opaque when scrolled
          : "bg-white/20 dark:bg-slate-900/20", // More transparent at top
        "backdrop-blur-2xl backdrop-saturate-150",
        "supports-[backdrop-filter]:bg-white/15 dark:supports-[backdrop-filter]:bg-slate-900/20",
        // Enhanced gradient overlay that responds to scroll
        "before:absolute before:inset-0 before:bg-gradient-to-r",
        isMounted && isScrolled
          ? "before:from-white/40 before:via-white/20 before:to-amber-50/30"
          : "before:from-white/30 before:via-white/10 before:to-amber-50/20",
        isMounted && isScrolled
          ? "dark:before:from-slate-900/50 dark:before:via-slate-900/25 dark:before:to-slate-800/35"
          : "dark:before:from-slate-900/40 dark:before:via-slate-900/15 dark:before:to-slate-800/25",
        "before:transition-all before:duration-300",
        "relative before:pointer-events-none",
        // Ensure proper stacking context
        "isolate",
        // Dynamic border that strengthens on scroll
        isMounted && isScrolled
          ? "border-b border-white/30 dark:border-slate-700/40"
          : "border-b border-white/20 dark:border-slate-700/30",
        "shadow-[0_1px_0_0_rgba(255,255,255,0.1)] dark:shadow-[0_1px_0_0_rgba(255,255,255,0.05)]",
        // Responsive padding
        "px-4 py-4 md:px-8 md:py-5",
        // Enhanced shadows that respond to scroll
        isMounted && isScrolled
          ? "shadow-xl shadow-black/8 dark:shadow-black/30"
          : "shadow-lg shadow-black/3 dark:shadow-black/20",
        // Hover effects
        "hover:bg-white/45 dark:hover:bg-slate-900/45",
        "hover:shadow-2xl hover:shadow-black/10 dark:hover:shadow-black/40",
        "hover:before:opacity-90",
        className
      )}
      style={{
        opacity: navbarOpacity,
        position: "sticky", // Force sticky positioning
        top: 0,
        zIndex: 9999, // Highest z-index priority
        isolation: "isolate", // Create new stacking context
        // Enhanced transform with slide effect when scrolling - only after mount
        transform: `scale(${navbarScale}) translateZ(0) translateY(${
          isMounted && isScrolled ? "0px" : "-2px"
        })`,
        boxShadow: `
          0 ${isMounted && isScrolled ? "12px" : "8px"} ${
          isMounted && isScrolled ? "40px" : "32px"
        } rgba(0, 0, 0, ${
          shadowIntensity * (isMounted && isScrolled ? 0.1 : 0.06)
        }),
          0 ${isMounted && isScrolled ? "4px" : "2px"} ${
          isMounted && isScrolled ? "12px" : "8px"
        } rgba(0, 0, 0, ${
          shadowIntensity * (isMounted && isScrolled ? 0.08 : 0.04)
        }),
          inset 0 1px 0 rgba(255, 255, 255, ${
            isMounted && isScrolled ? 0.4 : 0.2
          }),
          0 0 0 1px rgba(255, 255, 255, ${isMounted && isScrolled ? 0.2 : 0.05})
        `,
        backdropFilter: `blur(${
          isMounted && isScrolled ? "28px" : "20px"
        }) saturate(${isMounted && isScrolled ? "180%" : "150%"})`,
        WebkitBackdropFilter: `blur(${
          isMounted && isScrolled ? "28px" : "20px"
        }) saturate(${isMounted && isScrolled ? "180%" : "150%"})`, // Safari support
      }}
    >
      <div className="container mx-auto">
        <div className="flex items-center justify-between">
          {/* Left Section - Logo/Back Button */}
          <div className="flex items-center gap-4">
            {showBackButton ? (
              <Link
                href={backHref}
                className={cn(
                  "flex items-center gap-2 text-slate-600 dark:text-slate-400",
                  "hover:text-blue-600 dark:hover:text-blue-400",
                  "transition-all duration-200 ease-out",
                  "hover:scale-105 active:scale-95"
                )}
              >
                <ArrowRight className="w-5 h-5" />
                <span className="hidden sm:inline">{backLabel}</span>
              </Link>
            ) : (
              <Link
                href="/"
                className={cn(
                  "flex items-center gap-4 group relative",
                  "transition-all duration-500 ease-out",
                  "hover:scale-105 active:scale-95",
                  "focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:ring-offset-2",
                  "focus:ring-offset-white dark:focus:ring-offset-slate-900",
                  "rounded-lg p-2 -m-2"
                )}
              >
                {/* Glow effect on hover */}
                <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-amber-400/0 via-amber-400/5 to-orange-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                <div className="relative w-12 h-12 md:w-16 md:h-16 rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110 overflow-hidden">
                  <Image
                    src="/diocese-logo.png"
                    alt="شعار مطرانية شبين القناطر"
                    fill
                    className="object-cover transition-all duration-500 group-hover:brightness-110"
                    priority
                  />
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-400/10 via-transparent to-orange-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                </div>
                <div className="hidden sm:block relative">
                  <h1 className="text-lg md:text-2xl font-bold bg-gradient-to-r from-slate-900 via-amber-800 to-slate-900 dark:from-white dark:via-amber-300 dark:to-white bg-clip-text text-transparent transition-all duration-500 group-hover:from-amber-600 group-hover:via-orange-500 group-hover:to-amber-600">
                    كورس المشورة
                  </h1>
                  <p className="text-xs md:text-sm text-slate-600 dark:text-slate-400 font-medium transition-colors duration-300 group-hover:text-amber-600 dark:group-hover:text-amber-400">
                    مطرانية شبين القناطر وتوابعها
                  </p>
                </div>
              </Link>
            )}
          </div>

          {/* Center Section - Navigation (Desktop) */}
          <nav className="hidden lg:flex items-center gap-2">
            {currentNavItems.map((item, index) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium",
                  "transition-all duration-300 ease-out",
                  "hover:bg-white/50 dark:hover:bg-slate-800/50",
                  "hover:shadow-lg hover:shadow-black/5",
                  "hover:scale-105 active:scale-95",
                  "focus:outline-none focus:ring-2 focus:ring-amber-500/50",
                  item.isActive
                    ? "text-amber-600 dark:text-amber-400 font-semibold bg-amber-50/80 dark:bg-amber-900/20 shadow-md"
                    : "text-slate-700 dark:text-slate-300 hover:text-amber-600 dark:hover:text-amber-400"
                )}
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                {/* Background gradient on hover */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/0 via-amber-500/5 to-orange-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Icon with enhanced styling */}
                <span className="relative z-10 transition-transform duration-300 group-hover:scale-110">
                  {item.icon}
                </span>

                {/* Text with animation */}
                <span className="relative z-10 text-sm transition-all duration-300 group-hover:translate-x-0.5">
                  {item.label}
                </span>

                {/* Active indicator */}
                {item.isActive && (
                  <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-1.5 h-1.5 bg-amber-500 rounded-full animate-pulse" />
                )}
              </Link>
            ))}
          </nav>

          {/* Right Section - Actions */}
          <div className="flex items-center gap-3">
            {/* User Menu (Desktop) */}
            {showUserMenu && userInfo && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "hidden md:flex items-center gap-2",
                      "hover:bg-slate-100 dark:hover:bg-slate-800",
                      "transition-all duration-200 ease-out",
                      "hover:scale-105 active:scale-95"
                    )}
                  >
                    <div className="w-8 h-8 rounded-full overflow-hidden">
                      {userInfo.avatar ? (
                        <Image
                          src={userInfo.avatar}
                          alt={userInfo.name}
                          width={32}
                          height={32}
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </div>
                      )}
                    </div>
                    <span className="text-sm font-medium">{userInfo.name}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link href="/profile" className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      الملف الشخصي
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <LogOut className="w-4 h-4" />
                    تسجيل الخروج
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Sign In Button (when not authenticated) */}
            {!showUserMenu && (
              <div className="hidden md:flex items-center gap-3">
                <Button
                  asChild
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "text-slate-600 dark:text-slate-300 hover:text-amber-600 dark:hover:text-amber-400",
                    "hover:bg-amber-50/50 dark:hover:bg-amber-900/20",
                    "transition-all duration-300 ease-out",
                    "hover:scale-105 active:scale-95"
                  )}
                >
                  <Link href="/signin">تسجيل الدخول</Link>
                </Button>

                <Button
                  asChild
                  className={cn(
                    "bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600",
                    "text-white font-semibold shadow-lg shadow-amber-500/25",
                    "hover:shadow-xl hover:shadow-amber-500/40",
                    "transition-all duration-300 ease-out",
                    "hover:scale-105 active:scale-95",
                    "border-0 relative overflow-hidden"
                  )}
                  size="sm"
                >
                  <Link href="/signup" className="relative z-10">
                    ابدأ الآن
                  </Link>
                </Button>
              </div>
            )}

            {/* Mobile Menu */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "lg:hidden relative",
                    "transition-all duration-300 ease-out",
                    "hover:scale-105 active:scale-95",
                    "hover:bg-amber-50/50 dark:hover:bg-amber-900/20",
                    "focus:outline-none focus:ring-2 focus:ring-amber-500/50",
                    "rounded-xl p-2"
                  )}
                >
                  <div className="relative w-5 h-5">
                    {isMobileMenuOpen ? (
                      <X className="w-5 h-5 text-amber-600 dark:text-amber-400 transition-transform duration-300 rotate-90" />
                    ) : (
                      <Menu className="w-5 h-5 text-slate-600 dark:text-slate-400 transition-transform duration-300 hover:text-amber-600 dark:hover:text-amber-400" />
                    )}
                  </div>
                </Button>
              </SheetTrigger>
              <SheetContent
                side="right"
                className="w-80 bg-white/30 dark:bg-slate-900/30 backdrop-blur-2xl backdrop-saturate-150 border-l border-white/20 dark:border-slate-700/30 shadow-2xl"
              >
                {/* Accessible title for screen readers */}
                <SheetHeader className="sr-only">
                  <SheetTitle>القائمة الرئيسية</SheetTitle>
                </SheetHeader>

                <div className="flex flex-col gap-6 pt-6">
                  {/* Logo section for mobile */}
                  <div className="flex items-center gap-3 p-4 bg-white/20 dark:bg-slate-800/20 backdrop-blur-xl rounded-xl border border-white/30 dark:border-slate-700/30 shadow-lg">
                    <div className="w-10 h-10 rounded-xl overflow-hidden shadow-md">
                      <Image
                        src="/diocese-logo.png"
                        alt="شعار مطرانية شبين القناطر"
                        width={40}
                        height={40}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h2 className="font-bold text-slate-900 dark:text-white">
                        مشورة
                      </h2>
                      <p className="text-xs text-slate-600 dark:text-slate-400">
                        مطرانية شبين القناطر
                      </p>
                    </div>
                  </div>

                  {/* User Info (Mobile) */}
                  {showUserMenu && userInfo && (
                    <div className="flex items-center gap-3 p-4 bg-white/15 dark:bg-slate-800/15 backdrop-blur-xl rounded-xl shadow-lg border border-white/25 dark:border-slate-700/25">
                      <div className="w-12 h-12 rounded-full overflow-hidden">
                        {userInfo.avatar ? (
                          <Image
                            src={userInfo.avatar}
                            alt={userInfo.name}
                            width={48}
                            height={48}
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <User className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900 dark:text-white">
                          {userInfo.name}
                        </p>
                        {userInfo.email && (
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {userInfo.email}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Navigation Items (Mobile) */}
                  <nav className="flex flex-col gap-3">
                    {currentNavItems.map((item, index) => (
                      <Link
                        key={item.href}
                        href={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={cn(
                          "group flex items-center gap-4 px-4 py-4 rounded-xl relative overflow-hidden",
                          "transition-all duration-300 ease-out",
                          "hover:bg-white/20 dark:hover:bg-slate-800/20",
                          "hover:backdrop-blur-xl",
                          "hover:shadow-md hover:scale-[1.02]",
                          "border border-transparent hover:border-white/30 dark:hover:border-slate-700/30",
                          item.isActive
                            ? "text-amber-600 dark:text-amber-400 font-semibold bg-amber-50/30 dark:bg-amber-900/15 backdrop-blur-xl shadow-md border-amber-200/40 dark:border-amber-800/30"
                            : "text-slate-700 dark:text-slate-300 hover:text-amber-600 dark:hover:text-amber-400"
                        )}
                        style={{
                          animationDelay: `${index * 100}ms`,
                        }}
                      >
                        {/* Background glow effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-400/0 via-amber-400/5 to-orange-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                        {/* Icon */}
                        <span className="relative z-10 w-5 h-5 flex items-center justify-center transition-transform duration-300 group-hover:scale-110">
                          {item.icon}
                        </span>

                        {/* Text */}
                        <span className="relative z-10 font-medium transition-all duration-300 group-hover:translate-x-1">
                          {item.label}
                        </span>

                        {/* Arrow indicator */}
                        <ArrowRight className="relative z-10 w-4 h-4 mr-auto opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0" />
                      </Link>
                    ))}
                  </nav>

                  {/* Mobile Actions */}
                  <div className="flex flex-col gap-3 pt-6 border-t border-white/20 dark:border-slate-700/30">
                    {showUserMenu ? (
                      <>
                        <Button
                          asChild
                          variant="outline"
                          className={cn(
                            "justify-start h-12 rounded-xl border-white/30 dark:border-slate-700/30",
                            "hover:bg-white/20 dark:hover:bg-slate-800/20",
                            "hover:backdrop-blur-xl",
                            "transition-all duration-300 ease-out hover:scale-[1.02]"
                          )}
                        >
                          <Link
                            href="/profile"
                            onClick={() => setIsMobileMenuOpen(false)}
                            className="flex items-center gap-3 w-full"
                          >
                            <User className="w-5 h-5" />
                            <span className="font-medium">الملف الشخصي</span>
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          className={cn(
                            "justify-start h-12 rounded-xl",
                            "text-red-600 dark:text-red-400 border-red-200/50 dark:border-red-800/50",
                            "hover:bg-red-50/50 dark:hover:bg-red-900/20",
                            "transition-all duration-300 ease-out hover:scale-[1.02]"
                          )}
                        >
                          <LogOut className="w-5 h-5 mr-3" />
                          <span className="font-medium">تسجيل الخروج</span>
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          asChild
                          variant="outline"
                          className={cn(
                            "justify-start h-12 rounded-xl border-white/30 dark:border-slate-700/30",
                            "hover:bg-white/20 dark:hover:bg-slate-800/20",
                            "hover:backdrop-blur-xl",
                            "transition-all duration-300 ease-out hover:scale-[1.02]"
                          )}
                        >
                          <Link
                            href="/signin"
                            onClick={() => setIsMobileMenuOpen(false)}
                            className="flex items-center gap-3 w-full font-medium"
                          >
                            تسجيل الدخول
                          </Link>
                        </Button>
                        <Button
                          asChild
                          className={cn(
                            "h-12 rounded-xl font-semibold",
                            "bg-gradient-to-r from-amber-500 to-orange-500",
                            "hover:from-amber-600 hover:to-orange-600",
                            "shadow-lg shadow-amber-500/25 hover:shadow-xl hover:shadow-amber-500/40",
                            "transition-all duration-300 ease-out hover:scale-[1.02]"
                          )}
                        >
                          <Link
                            href="/signup"
                            onClick={() => setIsMobileMenuOpen(false)}
                            className="flex items-center gap-3 w-full justify-center"
                          >
                            <Sparkles className="w-5 h-5" />
                            <span>ابدأ الآن</span>
                          </Link>
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}
