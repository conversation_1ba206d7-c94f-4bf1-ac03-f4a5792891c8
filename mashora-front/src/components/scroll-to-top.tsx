"use client";

import { useEffect, useCallback } from "react";
import { usePathname } from "next/navigation";

/**
 * ScrollToTop component that automatically scrolls to the top
 * when the route changes in Next.js 13+ App Router
 *
 * Features:
 * - Smooth scrolling for better UX
 * - Performance optimized with requestAnimationFrame
 * - Only scrolls when necessary (not already at top)
 * - Handles edge cases and errors gracefully
 * - Works with RTL layout
 */
export default function ScrollToTop() {
  const pathname = usePathname();

  const scrollToTop = useCallback(() => {
    try {
      // Check if window is available (SSR safety)
      if (typeof window === "undefined") return;

      // Use requestAnimationFrame for better performance
      if (typeof requestAnimationFrame !== "undefined") {
        requestAnimationFrame(() => {
          try {
            // Check if we're not already at the top
            if (window.scrollY > 0 || window.scrollX > 0) {
              window.scrollTo({
                top: 0,
                left: 0,
                behavior: "smooth", // Smooth scrolling for better UX
              });
            }
          } catch (error) {
            // Fallback for browsers that don't support smooth scrolling
            console.warn(
              "Smooth scrolling not supported, using instant scroll"
            );
            window.scrollTo(0, 0);
          }
        });
      } else {
        // Fallback if requestAnimationFrame is not available
        window.scrollTo(0, 0);
      }
    } catch (error) {
      console.warn("ScrollToTop error:", error);
    }
  }, []);

  useEffect(() => {
    // Check if window is available (SSR safety)
    if (typeof window === "undefined") return;

    // Small delay to ensure the new page has rendered
    const timer = setTimeout(scrollToTop, 100);

    return () => clearTimeout(timer);
  }, [pathname, scrollToTop]);

  // Also handle scroll restoration for better UX
  useEffect(() => {
    // Check if window is available (SSR safety)
    if (typeof window === "undefined") return;

    try {
      // Disable browser's scroll restoration to prevent conflicts
      if ("scrollRestoration" in window.history) {
        window.history.scrollRestoration = "manual";
      }
    } catch (error) {
      console.warn("Could not set scroll restoration:", error);
    }
  }, []);

  return null;
}
