import Head from "next/head"

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: string
  noindex?: boolean
}

export function SEOHead({
  title = "كورس الإعداد للزواج - مشورة",
  description = "استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته",
  keywords = "زواج, إعداد للزواج, كورس, مسيحي, أبرشية, شبين القناطر",
  image = "/og-image.png",
  url = "https://mashora.diocese-shibin.org",
  type = "website",
  noindex = false,
}: SEOHeadProps) {
  const fullTitle = title.includes("مشورة") ? title : `${title} - مشورة`

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="مطرانية شبين القناطر" />
      <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />

      {/* Robots */}
      <meta name="robots" content={noindex ? "noindex, nofollow" : "index, follow"} />
      <meta name="googlebot" content={noindex ? "noindex, nofollow" : "index, follow"} />

      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="مشورة - مطرانية شبين القناطر" />
      <meta property="og:locale" content="ar_EG" />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />

      {/* Canonical URL */}
      <link rel="canonical" href={url} />

      {/* Alternate Languages */}
      <link rel="alternate" hrefLang="ar" href={url} />
      <link rel="alternate" hrefLang="en" href={`${url}/en`} />
      <link rel="alternate" hrefLang="x-default" href={url} />

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Course",
            name: "كورس الإعداد للزواج",
            description: description,
            provider: {
              "@type": "Organization",
              name: "مطرانية شبين القناطر",
              url: "https://diocese-shibin.org",
            },
            courseMode: "online",
            inLanguage: "ar",
            offers: {
              "@type": "Offer",
              price: "200",
              priceCurrency: "EGP",
            },
          }),
        }}
      />
    </Head>
  )
}
