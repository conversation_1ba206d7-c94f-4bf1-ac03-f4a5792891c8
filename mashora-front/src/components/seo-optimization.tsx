import Head from "next/head"

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: string
  locale?: string
  siteName?: string
}

export function SEOOptimization({
  title = "كورس الإعداد للزواج - مطرانية شبين القناطر",
  description = "استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته. كورس إجباري لكل المخطوبين في مطرانية شبين القناطر.",
  keywords = "زواج, إعداد للزواج, كورس, مسيحي, أبرشية, شبين القناطر, مشورة, تأهيل للزواج, الحياة الزوجية, التواصل الزوجي",
  image = "/og-image.jpg",
  url = "https://mashora.diocese-shibin.org",
  type = "website",
  locale = "ar_EG",
  siteName = "مشورة - مطرانية شبين القناطر",
}: SEOProps) {
  const fullTitle = title.includes("مطرانية") ? title : `${title} - مطرانية شبين القناطر`

  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "WebSite",
        "@id": `${url}/#website`,
        url: url,
        name: siteName,
        description: description,
        inLanguage: locale,
        potentialAction: {
          "@type": "SearchAction",
          target: {
            "@type": "EntryPoint",
            urlTemplate: `${url}/search?q={search_term_string}`,
          },
          "query-input": "required name=search_term_string",
        },
      },
      {
        "@type": "Organization",
        "@id": `${url}/#organization`,
        name: "مطرانية شبين القناطر",
        alternateName: "Diocese of Shibin El Qanater",
        url: url,
        logo: {
          "@type": "ImageObject",
          url: `${url}/diocese-logo.png`,
          width: 200,
          height: 200,
        },
        contactPoint: {
          "@type": "ContactPoint",
          telephone: "+20-13-123-4567",
          contactType: "customer service",
          email: "<EMAIL>",
          availableLanguage: ["Arabic", "English"],
        },
        address: {
          "@type": "PostalAddress",
          addressLocality: "شبين القناطر",
          addressRegion: "القليوبية",
          addressCountry: "EG",
        },
        sameAs: ["https://facebook.com/shibin.diocese", "https://youtube.com/shibin.diocese"],
      },
      {
        "@type": "Course",
        "@id": `${url}/#course`,
        name: "كورس الإعداد للزواج",
        alternateName: "Premarital Counseling Course",
        description: description,
        provider: {
          "@id": `${url}/#organization`,
        },
        courseMode: "online",
        educationalLevel: "beginner",
        inLanguage: "ar",
        timeRequired: "P2W",
        numberOfCredits: 8,
        coursePrerequisites: "خطوبة رسمية",
        offers: {
          "@type": "Offer",
          price: "200",
          priceCurrency: "EGP",
          availability: "https://schema.org/InStock",
          validFrom: "2024-01-01",
          category: "Religious Education",
        },
        hasCourseInstance: {
          "@type": "CourseInstance",
          courseMode: "online",
          instructor: {
            "@type": "Person",
            name: "فريق الإرشاد الزوجي",
          },
        },
        aggregateRating: {
          "@type": "AggregateRating",
          ratingValue: "4.9",
          reviewCount: "500",
          bestRating: "5",
          worstRating: "1",
        },
      },
      {
        "@type": "WebPage",
        "@id": `${url}/#webpage`,
        url: url,
        name: fullTitle,
        description: description,
        inLanguage: locale,
        isPartOf: {
          "@id": `${url}/#website`,
        },
        about: {
          "@id": `${url}/#course`,
        },
        primaryImageOfPage: {
          "@type": "ImageObject",
          url: image,
          width: 1200,
          height: 630,
        },
        datePublished: "2024-01-01",
        dateModified: new Date().toISOString().split("T")[0],
        breadcrumb: {
          "@type": "BreadcrumbList",
          itemListElement: [
            {
              "@type": "ListItem",
              position: 1,
              name: "الرئيسية",
              item: url,
            },
            {
              "@type": "ListItem",
              position: 2,
              name: "كورس الإعداد للزواج",
              item: `${url}/course`,
            },
          ],
        },
      },
    ],
  }

  return (
    <Head>
      {/* Primary Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="title" content={fullTitle} />
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="مطرانية شبين القناطر" />
      <meta name="creator" content="مطرانية شبين القناطر" />
      <meta name="publisher" content="مطرانية شبين القناطر" />
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />

      {/* Viewport and Mobile */}
      <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="مشورة" />

      {/* Theme Colors */}
      <meta name="theme-color" content="#f59e0b" />
      <meta name="msapplication-TileColor" content="#f59e0b" />
      <meta name="msapplication-navbutton-color" content="#f59e0b" />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={fullTitle} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      <meta property="og:locale:alternate" content="en_US" />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={url} />
      <meta property="twitter:title" content={fullTitle} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={image} />
      <meta property="twitter:image:alt" content={fullTitle} />
      <meta property="twitter:creator" content="@shibin_diocese" />
      <meta property="twitter:site" content="@shibin_diocese" />

      {/* Additional Meta Tags */}
      <meta name="format-detection" content="telephone=yes" />
      <meta name="geo.region" content="EG-KB" />
      <meta name="geo.placename" content="شبين القناطر" />
      <meta name="geo.position" content="30.1626;31.3117" />
      <meta name="ICBM" content="30.1626, 31.3117" />

      {/* Canonical URL */}
      <link rel="canonical" href={url} />

      {/* Alternate Languages */}
      <link rel="alternate" hrefLang="ar" href={url} />
      <link rel="alternate" hrefLang="en" href={`${url}/en`} />
      <link rel="alternate" hrefLang="x-default" href={url} />

      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />

      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/manifest.json" />

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData, null, 2),
        }}
      />

      {/* Additional SEO Scripts */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // Google Analytics 4
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'GA_MEASUREMENT_ID');
            
            // Facebook Pixel
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', 'FB_PIXEL_ID');
            fbq('track', 'PageView');
          `,
        }}
      />
    </Head>
  )
}
