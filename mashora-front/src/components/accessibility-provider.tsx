"use client";

import type React from "react";

import { createContext, useContext, useEffect, useState } from "react";

interface AccessibilitySettings {
  highContrast: boolean;
  reducedMotion: boolean;
  fontSize: "small" | "medium" | "large" | "extra-large";
  screenReader: boolean;
}

interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateSettings: (settings: Partial<AccessibilitySettings>) => void;
  announceToScreenReader: (message: string) => void;
}

const AccessibilityContext = createContext<AccessibilityContextType | null>(
  null
);

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error(
      "useAccessibility must be used within AccessibilityProvider"
    );
  }
  return context;
}

export function AccessibilityProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    highContrast: false,
    reducedMotion: false,
    fontSize: "medium",
    screenReader: false,
  });

  const [announcements, setAnnouncements] = useState<string[]>([]);

  useEffect(() => {
    // Check if window is available (SSR safety)
    if (typeof window === "undefined") return;

    try {
      // Load settings from localStorage
      const savedSettings = localStorage.getItem("accessibility-settings");
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }

      // Detect system preferences
      const prefersReducedMotion = window.matchMedia(
        "(prefers-reduced-motion: reduce)"
      ).matches;
      const prefersHighContrast = window.matchMedia(
        "(prefers-contrast: high)"
      ).matches;

      setSettings((prev) => ({
        ...prev,
        reducedMotion: prefersReducedMotion,
        highContrast: prefersHighContrast,
      }));

      // Detect screen reader
      const hasScreenReader =
        window.navigator.userAgent.includes("NVDA") ||
        window.navigator.userAgent.includes("JAWS") ||
        window.speechSynthesis?.getVoices().length > 0;

      if (hasScreenReader) {
        setSettings((prev) => ({ ...prev, screenReader: true }));
      }
    } catch (error) {
      console.warn("AccessibilityProvider initialization error:", error);
    }
  }, []);

  useEffect(() => {
    // Check if window is available (SSR safety)
    if (typeof window === "undefined") return;

    try {
      // Apply settings to document
      const root = document.documentElement;

      // High contrast
      if (settings.highContrast) {
        root.classList.add("high-contrast");
      } else {
        root.classList.remove("high-contrast");
      }

      // Reduced motion
      if (settings.reducedMotion) {
        root.classList.add("reduce-motion");
      } else {
        root.classList.remove("reduce-motion");
      }

      // Font size
      root.classList.remove(
        "font-small",
        "font-medium",
        "font-large",
        "font-extra-large"
      );
      root.classList.add(`font-${settings.fontSize}`);

      // Save to localStorage
      localStorage.setItem("accessibility-settings", JSON.stringify(settings));
    } catch (error) {
      console.warn("AccessibilityProvider settings application error:", error);
    }
  }, [settings]);

  const updateSettings = (newSettings: Partial<AccessibilitySettings>) => {
    setSettings((prev) => ({ ...prev, ...newSettings }));
  };

  const announceToScreenReader = (message: string) => {
    setAnnouncements((prev) => [...prev, message]);

    // Remove announcement after it's been read
    setTimeout(() => {
      setAnnouncements((prev) => prev.slice(1));
    }, 1000);
  };

  return (
    <AccessibilityContext.Provider
      value={{ settings, updateSettings, announceToScreenReader }}
    >
      {children}

      {/* Screen reader announcements */}
      <div
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
        role="status"
      >
        {announcements.map((announcement, index) => (
          <div key={index}>{announcement}</div>
        ))}
      </div>

      {/* Skip to main content link */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded-md focus:shadow-lg"
      >
        تخطي إلى المحتوى الرئيسي
      </a>
    </AccessibilityContext.Provider>
  );
}
