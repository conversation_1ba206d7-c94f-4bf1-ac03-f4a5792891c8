<?php

namespace Tests\Unit\Services;

use App\Models\FileUpload;
use App\Models\User;
use App\Services\FileUploadService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class FileUploadServiceTest extends TestCase
{
    use RefreshDatabase;

    private FileUploadService $fileUploadService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->fileUploadService = new FileUploadService();
        Storage::fake('private');
    }

    public function test_upload_file_creates_file_upload_record(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $this->assertArrayHasKey('file_upload', $result);
        $this->assertArrayHasKey('file_path', $result);
        $this->assertArrayHasKey('file_url', $result);

        $fileUpload = $result['file_upload'];
        $this->assertInstanceOf(FileUpload::class, $fileUpload);
        $this->assertEquals($user->id, $fileUpload->user_id);
        $this->assertEquals('avatar', $fileUpload->file_type);
        $this->assertEquals('test.jpg', $fileUpload->original_name);
        $this->assertEquals('clean', $fileUpload->virus_scan_status);
    }

    public function test_upload_file_stores_file_in_correct_location(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $result = $this->fileUploadService->uploadFile($file, $user, 'national_id_front');

        $filePath = $result['file_path'];
        $this->assertTrue(Storage::disk('private')->exists($filePath));
        $this->assertStringContains("uploads/users/{$user->id}/national_id_front", $filePath);
    }

    public function test_upload_file_generates_unique_filename(): void
    {
        $user = User::factory()->create();
        $file1 = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $file2 = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $result1 = $this->fileUploadService->uploadFile($file1, $user, 'avatar');
        $result2 = $this->fileUploadService->uploadFile($file2, $user, 'avatar');

        $this->assertNotEquals($result1['file_path'], $result2['file_path']);
    }

    public function test_upload_file_calculates_checksums(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $fileUpload = $result['file_upload'];
        $this->assertNotNull($fileUpload->checksum_md5);
        $this->assertNotNull($fileUpload->checksum_sha256);
        $this->assertEquals(32, strlen($fileUpload->checksum_md5));
        $this->assertEquals(64, strlen($fileUpload->checksum_sha256));
    }

    public function test_upload_file_validates_file_size(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('large.jpg', 1000, 1000)->size(6144); // 6MB

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('File size exceeds maximum allowed size of 5MB');

        $this->fileUploadService->uploadFile($file, $user, 'avatar');
    }

    public function test_upload_file_validates_file_extension(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->create('document.pdf', 1024);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid file type. Only JPEG and PNG files are allowed');

        $this->fileUploadService->uploadFile($file, $user, 'avatar');
    }

    public function test_upload_file_validates_mime_type(): void
    {
        $user = User::factory()->create();
        // Create a file with wrong MIME type
        $file = UploadedFile::fake()->create('test.jpg', 1024, 'application/pdf');

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid MIME type. Only JPEG and PNG images are allowed');

        $this->fileUploadService->uploadFile($file, $user, 'avatar');
    }

    public function test_upload_file_validates_image_dimensions_for_national_id(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('small.jpg', 100, 100)->size(512); // Too small

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Image dimensions too small');

        $this->fileUploadService->uploadFile($file, $user, 'national_id_front');
    }

    public function test_upload_file_allows_small_avatar_images(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('small_avatar.jpg', 100, 100)->size(512);

        // Should not throw exception for avatar
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $this->assertArrayHasKey('file_upload', $result);
    }

    public function test_delete_file_removes_file_and_record(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');
        $filePath = $result['file_path'];

        // Verify file exists
        $this->assertTrue(Storage::disk('private')->exists($filePath));
        $this->assertDatabaseHas('file_uploads', ['file_path' => $filePath]);

        // Delete file
        $deleted = $this->fileUploadService->deleteFile($filePath);

        $this->assertTrue($deleted);
        $this->assertFalse(Storage::disk('private')->exists($filePath));
        $this->assertDatabaseMissing('file_uploads', ['file_path' => $filePath]);
    }

    public function test_delete_file_returns_false_for_nonexistent_file(): void
    {
        $deleted = $this->fileUploadService->deleteFile('nonexistent/path.jpg');

        $this->assertFalse($deleted);
    }

    public function test_get_file_content_returns_content(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');
        $filePath = $result['file_path'];

        $content = $this->fileUploadService->getFileContent($filePath);

        $this->assertNotNull($content);
        $this->assertIsString($content);
    }

    public function test_get_file_content_returns_null_for_nonexistent_file(): void
    {
        $content = $this->fileUploadService->getFileContent('nonexistent/path.jpg');

        $this->assertNull($content);
    }

    public function test_get_file_mime_type_returns_correct_type(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');
        $filePath = $result['file_path'];

        $mimeType = $this->fileUploadService->getFileMimeType($filePath);

        $this->assertEquals('image/jpeg', $mimeType);
    }

    public function test_get_file_mime_type_returns_null_for_nonexistent_file(): void
    {
        $mimeType = $this->fileUploadService->getFileMimeType('nonexistent/path.jpg');

        $this->assertNull($mimeType);
    }

    public function test_upload_file_processes_large_images(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('large.jpg', 3000, 3000)->size(4096); // Large dimensions

        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $this->assertArrayHasKey('file_upload', $result);
        
        // File should be processed and stored
        $filePath = $result['file_path'];
        $this->assertTrue(Storage::disk('private')->exists($filePath));
    }

    public function test_upload_file_sets_virus_scan_status(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $fileUpload = $result['file_upload'];
        $this->assertEquals('clean', $fileUpload->virus_scan_status);
        $this->assertNotNull($fileUpload->virus_scan_result);
        $this->assertIsArray($fileUpload->virus_scan_result);
    }
}
