<?php

namespace Tests\Unit\Rules;

use App\Rules\SecureImageUpload;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class SecureImageUploadTest extends TestCase
{
    public function test_validates_valid_jpeg_image(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertEmpty($errors);
    }

    public function test_validates_valid_png_image(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->image('test.png', 800, 600)->size(2048);

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertEmpty($errors);
    }

    public function test_rejects_non_file_input(): void
    {
        $rule = new SecureImageUpload();

        $errors = [];
        $rule->validate('test_field', 'not-a-file', function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertCount(1, $errors);
        $this->assertStringContains('must be a valid file', $errors[0]);
    }

    public function test_rejects_invalid_file_upload(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->create('test.jpg', 1024);
        
        // Simulate upload error
        $file->error = UPLOAD_ERR_PARTIAL;

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertCount(1, $errors);
        $this->assertStringContains('upload failed', $errors[0]);
    }

    public function test_rejects_oversized_files(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->image('large.jpg', 1000, 1000)->size(6144); // 6MB

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertCount(1, $errors);
        $this->assertStringContains('must not be larger than 5MB', $errors[0]);
    }

    public function test_rejects_invalid_mime_types(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertCount(1, $errors);
        $this->assertStringContains('must be a JPEG or PNG image', $errors[0]);
    }

    public function test_rejects_invalid_file_extensions(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->create('test.gif', 1024, 'image/gif');

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertCount(1, $errors);
        $this->assertStringContains('must have a valid image extension', $errors[0]);
    }

    public function test_rejects_images_with_small_dimensions_when_required(): void
    {
        $rule = new SecureImageUpload('national_id', true); // Require minimum dimensions
        $file = UploadedFile::fake()->image('small.jpg', 100, 100)->size(512);

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertCount(1, $errors);
        $this->assertStringContains('must be at least 200x200 pixels', $errors[0]);
    }

    public function test_allows_small_images_when_not_required(): void
    {
        $rule = new SecureImageUpload('avatar', false); // Don't require minimum dimensions
        $file = UploadedFile::fake()->image('small.jpg', 100, 100)->size(512);

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertEmpty($errors);
    }

    public function test_rejects_images_with_large_dimensions(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->image('huge.jpg', 3000, 3000)->size(4096);

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertCount(1, $errors);
        $this->assertStringContains('must not exceed 2048x2048 pixels', $errors[0]);
    }

    public function test_avatar_rule_factory_method(): void
    {
        $rule = SecureImageUpload::avatar();
        $file = UploadedFile::fake()->image('small.jpg', 100, 100)->size(512);

        $errors = [];
        $rule->validate('avatar', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        // Should allow small images for avatar
        $this->assertEmpty($errors);
    }

    public function test_national_id_rule_factory_method(): void
    {
        $rule = SecureImageUpload::nationalId();
        $file = UploadedFile::fake()->image('small.jpg', 100, 100)->size(512);

        $errors = [];
        $rule->validate('national_id', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        // Should reject small images for national ID
        $this->assertCount(1, $errors);
        $this->assertStringContains('must be at least 200x200 pixels', $errors[0]);
    }

    public function test_rejects_files_with_suspicious_content(): void
    {
        $rule = new SecureImageUpload();
        
        // Create a fake image file with suspicious content
        $tempFile = tmpfile();
        $tempPath = stream_get_meta_data($tempFile)['uri'];
        
        // Write image header followed by suspicious content
        fwrite($tempFile, "\xFF\xD8\xFF\xE0"); // JPEG header
        fwrite($tempFile, "<?php echo 'malicious code'; ?>"); // Suspicious content
        fseek($tempFile, 0);
        
        $file = new UploadedFile($tempPath, 'test.jpg', 'image/jpeg', null, true);

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertNotEmpty($errors);
        $this->assertStringContains('suspicious content', $errors[0]);

        fclose($tempFile);
    }

    public function test_rejects_files_with_double_extensions(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->image('test.php.jpg', 800, 600)->size(2048);

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        $this->assertNotEmpty($errors);
        $this->assertStringContains('suspicious extensions', $errors[0]);
    }

    public function test_validates_file_content_matches_extension(): void
    {
        $rule = new SecureImageUpload();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);

        $errors = [];
        $rule->validate('test_field', $file, function ($message) use (&$errors) {
            $errors[] = $message;
        });

        // Should pass if content matches extension
        $this->assertEmpty($errors);
    }
}
