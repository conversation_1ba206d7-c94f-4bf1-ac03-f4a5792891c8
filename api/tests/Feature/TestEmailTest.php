<?php

namespace Tests\Feature;

use App\Mail\TestEmail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class TestEmailTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    public function test_test_email_can_be_sent(): void
    {
        $testMessage = 'This is a custom test message';
        $email = '<EMAIL>';

        Mail::to($email)->send(new TestEmail($testMessage));

        Mail::assertSent(TestEmail::class, function ($mail) use ($testMessage) {
            return $mail->testMessage === $testMessage;
        });
    }

    public function test_test_email_has_correct_subject(): void
    {
        $testEmail = new TestEmail;
        $envelope = $testEmail->envelope();

        $this->assertEquals('Test Email - Mashora Courses Email Configuration', $envelope->subject);
    }

    public function test_test_email_uses_correct_view(): void
    {
        $testEmail = new TestEmail;
        $content = $testEmail->content();

        $this->assertEquals('emails.test', $content->view);
    }

    public function test_test_email_has_correct_data(): void
    {
        $customMessage = 'Custom test message';
        $testEmail = new TestEmail($customMessage);
        $content = $testEmail->content();

        $this->assertArrayHasKey('testMessage', $content->with);
        $this->assertArrayHasKey('timestamp', $content->with);
        $this->assertArrayHasKey('appName', $content->with);
        $this->assertEquals($customMessage, $content->with['testMessage']);
        $this->assertEquals(config('app.name'), $content->with['appName']);
    }

    public function test_test_email_uses_default_message_when_none_provided(): void
    {
        $testEmail = new TestEmail;

        $this->assertEquals('This is a test email from Mashora Courses!', $testEmail->testMessage);
    }
}
