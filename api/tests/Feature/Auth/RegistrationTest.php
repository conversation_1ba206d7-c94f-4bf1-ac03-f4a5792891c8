<?php

namespace Tests\Feature\Auth;

use App\Jobs\SendWelcomeEmail;
use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class RegistrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Queue::fake();
    }

    public function test_user_can_register_with_valid_data(): void
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+201234567890',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'national_id' => '12345678901234',
            'date_of_birth' => '1990-01-01',
            'address' => '123 Main St, Cairo, Egypt',
            'church_belong' => 'St. Mark Church',
            'father_of_confession' => 'Father <PERSON>',
            'confession_church' => 'St. Mark Church',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'user' => [
                    'id',
                    'first_name',
                    'last_name',
                    'full_name',
                    'email',
                    'phone',
                    'profile' => [
                        'church_belong',
                        'father_of_confession',
                        'confession_church',
                        'registration_step',
                    ],
                ],
                'token',
                'token_type',
            ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '+201234567890',
            'national_id' => '12345678901234',
            'email_verified' => false,
            'status' => 'active',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertDatabaseHas('user_profiles', [
            'user_id' => $user->id,
            'church_belong' => 'St. Mark Church',
            'father_of_confession' => 'Father John',
            'confession_church' => 'St. Mark Church',
            'registration_step' => 1,
        ]);

        Queue::assertPushed(SendWelcomeEmail::class);
    }

    public function test_registration_requires_valid_email(): void
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => 'invalid-email',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_registration_requires_unique_email(): void
    {
        User::factory()->create(['email' => '<EMAIL>']);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_registration_requires_password_confirmation(): void
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'different_password',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }

    public function test_registration_requires_strong_password(): void
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => '123',
            'password_confirmation' => '123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }

    public function test_registration_requires_unique_phone(): void
    {
        User::factory()->create(['phone' => '+201234567890']);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+201234567890',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['phone']);
    }

    public function test_registration_requires_unique_national_id(): void
    {
        User::factory()->create(['national_id' => '12345678901234']);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'national_id' => '12345678901234',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['national_id']);
    }

    public function test_registration_validates_date_of_birth(): void
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'date_of_birth' => '2030-01-01', // Future date
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['date_of_birth']);
    }

    public function test_registration_creates_user_profile(): void
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'church_belong' => 'St. Mark Church',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertInstanceOf(UserProfile::class, $user->profile);
        $this->assertEquals('St. Mark Church', $user->profile->church_belong);
        $this->assertEquals(1, $user->profile->registration_step);
    }

    public function test_registration_returns_auth_token(): void
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'token',
                'token_type',
            ]);

        $this->assertEquals('Bearer', $response->json('token_type'));
        $this->assertNotEmpty($response->json('token'));
    }
}
