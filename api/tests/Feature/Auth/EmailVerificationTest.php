<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\URL;
use Tests\TestCase;

class EmailVerificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    public function test_user_can_verify_email_with_valid_link(): void
    {
        $user = User::factory()->create([
            'email_verified' => false,
            'email_verified_at' => null,
        ]);

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'id' => $user->id,
                'hash' => sha1($user->email),
            ]
        );

        $response = $this->get($verificationUrl);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Email verified successfully',
            ]);

        $user->refresh();
        $this->assertTrue($user->email_verified);
        $this->assertNotNull($user->email_verified_at);
    }

    public function test_email_verification_fails_with_invalid_user_id(): void
    {
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'id' => 999999, // Non-existent user ID
                'hash' => sha1('<EMAIL>'),
            ]
        );

        $response = $this->get($verificationUrl);

        $response->assertStatus(400)
            ->assertJson([
                'message' => 'Invalid verification link',
            ]);
    }

    public function test_email_verification_fails_with_invalid_hash(): void
    {
        $user = User::factory()->create([
            'email_verified' => false,
        ]);

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'id' => $user->id,
                'hash' => 'invalid-hash',
            ]
        );

        $response = $this->get($verificationUrl);

        $response->assertStatus(400)
            ->assertJson([
                'message' => 'Invalid verification link',
            ]);
    }

    public function test_already_verified_email_returns_appropriate_message(): void
    {
        $user = User::factory()->create([
            'email_verified' => true,
            'email_verified_at' => now(),
        ]);

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'id' => $user->id,
                'hash' => sha1($user->email),
            ]
        );

        $response = $this->get($verificationUrl);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Email already verified',
            ]);
    }

    public function test_user_can_resend_verification_email(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified' => false,
        ]);

        $response = $this->postJson('/api/v1/auth/resend-verification', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Verification email sent',
            ]);

        Notification::assertSentTo($user, \App\Notifications\VerifyEmailNotification::class);
    }

    public function test_resend_verification_fails_for_nonexistent_user(): void
    {
        $response = $this->postJson('/api/v1/auth/resend-verification', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_resend_verification_for_already_verified_user(): void
    {
        User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified' => true,
            'email_verified_at' => now(),
        ]);

        $response = $this->postJson('/api/v1/auth/resend-verification', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Email already verified',
            ]);
    }

    public function test_resend_verification_requires_valid_email_format(): void
    {
        $response = $this->postJson('/api/v1/auth/resend-verification', [
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_resend_verification_requires_email(): void
    {
        $response = $this->postJson('/api/v1/auth/resend-verification', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_verification_link_expires(): void
    {
        $user = User::factory()->create([
            'email_verified' => false,
        ]);

        // Create an expired verification URL
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->subMinutes(60), // Expired 60 minutes ago
            [
                'id' => $user->id,
                'hash' => sha1($user->email),
            ]
        );

        $response = $this->get($verificationUrl);

        $response->assertStatus(403); // Forbidden due to expired signature
    }

    public function test_verification_link_requires_signature(): void
    {
        $user = User::factory()->create([
            'email_verified' => false,
        ]);

        // Create URL without signature
        $verificationUrl = route('verification.verify', [
            'id' => $user->id,
            'hash' => sha1($user->email),
        ]);

        $response = $this->get($verificationUrl);

        $response->assertStatus(403); // Forbidden due to missing signature
    }

    public function test_user_model_has_verification_methods(): void
    {
        $user = User::factory()->create([
            'email_verified' => false,
            'email_verified_at' => null,
        ]);

        $this->assertFalse($user->hasVerifiedEmail());
        $this->assertEquals($user->email, $user->getEmailForVerification());

        $user->markEmailAsVerified();

        $this->assertTrue($user->hasVerifiedEmail());
        $this->assertNotNull($user->email_verified_at);
    }
}
