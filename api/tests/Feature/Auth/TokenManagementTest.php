<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class TokenManagementTest extends TestCase
{
    use RefreshDatabase;

    public function test_authenticated_user_can_refresh_token(): void
    {
        $user = User::factory()->create();
        $oldToken = $user->createToken('old-token');

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $oldToken->plainTextToken,
        ])->postJson('/api/v1/auth/refresh');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'token',
                'token_type',
                'user' => [
                    'id',
                    'first_name',
                    'last_name',
                    'email',
                ],
            ]);

        $this->assertEquals('Bearer', $response->json('token_type'));
        $this->assertNotEmpty($response->json('token'));

        // Old token should be deleted
        $user->refresh();
        $this->assertEquals(1, $user->tokens()->count());
        $this->assertNotEquals($oldToken->plainTextToken, $response->json('token'));
    }

    public function test_refresh_token_requires_authentication(): void
    {
        $response = $this->postJson('/api/v1/auth/refresh');

        $response->assertStatus(401);
    }

    public function test_refresh_token_with_invalid_token_fails(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
        ])->postJson('/api/v1/auth/refresh');

        $response->assertStatus(401);
    }

    public function test_user_can_get_current_user_info(): void
    {
        $user = User::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        Sanctum::actingAs($user);

        $response = $this->getJson('/api/v1/auth/me');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'user' => [
                    'id',
                    'first_name',
                    'last_name',
                    'full_name',
                    'email',
                    'status',
                    'email_verified',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'user' => [
                    'id' => $user->id,
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'full_name' => 'John Doe',
                    'email' => '<EMAIL>',
                ],
            ]);
    }

    public function test_me_endpoint_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/auth/me');

        $response->assertStatus(401);
    }

    public function test_me_endpoint_includes_user_profile(): void
    {
        $user = User::factory()->create();
        $user->profile()->create([
            'church_belong' => 'St. Mark Church',
            'father_of_confession' => 'Father John',
            'registration_step' => 2,
        ]);

        Sanctum::actingAs($user);

        $response = $this->getJson('/api/v1/auth/me');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'user' => [
                    'profile' => [
                        'church_belong',
                        'father_of_confession',
                        'registration_step',
                    ],
                ],
            ])
            ->assertJson([
                'user' => [
                    'profile' => [
                        'church_belong' => 'St. Mark Church',
                        'father_of_confession' => 'Father John',
                        'registration_step' => 2,
                    ],
                ],
            ]);
    }

    public function test_token_abilities_are_properly_set(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token', ['*']);

        $this->assertTrue($token->accessToken->can('*'));
    }

    public function test_multiple_tokens_can_exist_for_user(): void
    {
        $user = User::factory()->create();
        
        $token1 = $user->createToken('device1');
        $token2 = $user->createToken('device2');
        $token3 = $user->createToken('device3');

        $this->assertEquals(3, $user->tokens()->count());
        $this->assertNotEquals($token1->plainTextToken, $token2->plainTextToken);
        $this->assertNotEquals($token2->plainTextToken, $token3->plainTextToken);
    }

    public function test_token_names_are_stored_correctly(): void
    {
        $user = User::factory()->create();
        
        $user->createToken('mobile-app');
        $user->createToken('web-browser');

        $tokenNames = $user->tokens()->pluck('name')->toArray();
        
        $this->assertContains('mobile-app', $tokenNames);
        $this->assertContains('web-browser', $tokenNames);
    }

    public function test_legacy_user_endpoint_still_works(): void
    {
        $user = User::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        Sanctum::actingAs($user);

        $response = $this->getJson('/api/v1/user');

        $response->assertStatus(200);
        
        // Should return the user model directly (not wrapped in UserResource)
        $this->assertEquals($user->id, $response->json('id'));
        $this->assertEquals('<EMAIL>', $response->json('email'));
    }

    public function test_token_expiration_is_handled_correctly(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token');

        // Token should be valid initially
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->getJson('/api/v1/auth/me');

        $response->assertStatus(200);

        // Delete the token to simulate expiration
        $token->accessToken->delete();

        // Clear any cached authentication state
        $this->app['auth']->forgetGuards();

        // Token should now be invalid
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->getJson('/api/v1/auth/me');

        $response->assertStatus(401);
    }
}
