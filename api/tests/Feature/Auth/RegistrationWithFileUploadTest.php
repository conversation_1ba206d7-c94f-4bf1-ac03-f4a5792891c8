<?php

namespace Tests\Feature\Auth;

use App\Jobs\SendWelcomeEmail;
use App\Models\FileUpload;
use App\Models\User;
use App\Services\FileUploadService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class RegistrationWithFileUploadTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Queue::fake();
        Storage::fake('private');
    }

    public function test_user_can_register_with_required_file_uploads(): void
    {
        $avatar = UploadedFile::fake()->image('avatar.jpg', 300, 300)->size(1024);
        $nationalIdFront = UploadedFile::fake()->image('id_front.jpg', 800, 600)->size(2048);
        $nationalIdBack = UploadedFile::fake()->image('id_back.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'avatar' => $avatar,
            'national_id_front' => $nationalIdFront,
            'national_id_back' => $nationalIdBack,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'user' => [
                    'id',
                    'first_name',
                    'last_name',
                    'email',
                    'avatar_url',
                    'national_id_front_url',
                    'national_id_back_url',
                    'identity_verification_status',
                ],
                'token',
                'token_type',
            ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('pending', $user->identity_verification_status);
        $this->assertNotNull($user->avatar_file_path);
        $this->assertNotNull($user->national_id_front_file_path);
        $this->assertNotNull($user->national_id_back_file_path);

        // Check that files were uploaded
        $this->assertDatabaseHas('file_uploads', [
            'user_id' => $user->id,
            'file_type' => 'avatar',
        ]);

        $this->assertDatabaseHas('file_uploads', [
            'user_id' => $user->id,
            'file_type' => 'national_id_front',
        ]);

        $this->assertDatabaseHas('file_uploads', [
            'user_id' => $user->id,
            'file_type' => 'national_id_back',
        ]);

        Queue::assertPushed(SendWelcomeEmail::class);
    }

    public function test_registration_requires_national_id_front_image(): void
    {
        $nationalIdBack = UploadedFile::fake()->image('id_back.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'national_id_back' => $nationalIdBack,
            // Missing national_id_front
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['national_id_front']);
    }

    public function test_registration_requires_national_id_back_image(): void
    {
        $nationalIdFront = UploadedFile::fake()->image('id_front.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'national_id_front' => $nationalIdFront,
            // Missing national_id_back
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['national_id_back']);
    }

    public function test_registration_works_without_avatar(): void
    {
        $nationalIdFront = UploadedFile::fake()->image('id_front.jpg', 800, 600)->size(2048);
        $nationalIdBack = UploadedFile::fake()->image('id_back.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'national_id_front' => $nationalIdFront,
            'national_id_back' => $nationalIdBack,
            // No avatar
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNull($user->avatar_file_path);
        $this->assertNotNull($user->national_id_front_file_path);
        $this->assertNotNull($user->national_id_back_file_path);
    }

    public function test_registration_rejects_invalid_file_types(): void
    {
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1024);
        $nationalIdFront = UploadedFile::fake()->image('id_front.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'national_id_front' => $nationalIdFront,
            'national_id_back' => $invalidFile, // Invalid file type
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['national_id_back']);
    }

    public function test_registration_rejects_oversized_files(): void
    {
        $oversizedFile = UploadedFile::fake()->image('large.jpg', 1000, 1000)->size(6144); // 6MB
        $nationalIdFront = UploadedFile::fake()->image('id_front.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'national_id_front' => $nationalIdFront,
            'national_id_back' => $oversizedFile, // Too large
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['national_id_back']);
    }

    public function test_registration_rejects_images_with_small_dimensions(): void
    {
        $smallImage = UploadedFile::fake()->image('small.jpg', 100, 100)->size(512); // Too small
        $nationalIdFront = UploadedFile::fake()->image('id_front.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'national_id_front' => $nationalIdFront,
            'national_id_back' => $smallImage, // Too small
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['national_id_back']);
    }

    public function test_registration_handles_file_upload_failure_gracefully(): void
    {
        // Mock the FileUploadService to throw an exception
        $this->mock(FileUploadService::class, function ($mock) {
            $mock->shouldReceive('uploadFile')
                ->andThrow(new \Exception('File upload failed'));
        });

        $nationalIdFront = UploadedFile::fake()->image('id_front.jpg', 800, 600)->size(2048);
        $nationalIdBack = UploadedFile::fake()->image('id_back.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'national_id_front' => $nationalIdFront,
            'national_id_back' => $nationalIdBack,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJson([
                'message' => 'Registration failed: File upload failed',
            ]);

        // User should not be created if file upload fails
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_registration_creates_file_upload_records(): void
    {
        $avatar = UploadedFile::fake()->image('avatar.jpg', 300, 300)->size(1024);
        $nationalIdFront = UploadedFile::fake()->image('id_front.jpg', 800, 600)->size(2048);
        $nationalIdBack = UploadedFile::fake()->image('id_back.jpg', 800, 600)->size(2048);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'avatar' => $avatar,
            'national_id_front' => $nationalIdFront,
            'national_id_back' => $nationalIdBack,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201);

        $user = User::where('email', '<EMAIL>')->first();

        // Check file upload records
        $avatarUpload = FileUpload::where('user_id', $user->id)
            ->where('file_type', 'avatar')
            ->first();
        $this->assertNotNull($avatarUpload);
        $this->assertEquals('avatar.jpg', $avatarUpload->original_name);
        $this->assertEquals('clean', $avatarUpload->virus_scan_status);

        $frontUpload = FileUpload::where('user_id', $user->id)
            ->where('file_type', 'national_id_front')
            ->first();
        $this->assertNotNull($frontUpload);
        $this->assertEquals('id_front.jpg', $frontUpload->original_name);

        $backUpload = FileUpload::where('user_id', $user->id)
            ->where('file_type', 'national_id_back')
            ->first();
        $this->assertNotNull($backUpload);
        $this->assertEquals('id_back.jpg', $backUpload->original_name);
    }
}
