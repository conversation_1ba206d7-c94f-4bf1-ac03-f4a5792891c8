<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON>vel\Sanctum\Sanctum;
use Tests\TestCase;

class LogoutTest extends TestCase
{
    use RefreshDatabase;

    public function test_authenticated_user_can_logout(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token');

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson('/api/v1/auth/logout');

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Logged out successfully',
            ]);

        // Token should be deleted
        $this->assertEquals(0, $user->tokens()->count());
    }

    public function test_logout_only_deletes_current_token(): void
    {
        $user = User::factory()->create();
        $token1 = $user->createToken('token1');
        $token2 = $user->createToken('token2');

        $this->assertEquals(2, $user->tokens()->count());

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token1->plainTextToken,
        ])->postJson('/api/v1/auth/logout');

        $response->assertStatus(200);

        // Only one token should remain
        $user->refresh();
        $this->assertEquals(1, $user->tokens()->count());
        $this->assertEquals('token2', $user->tokens()->first()->name);
    }

    public function test_logout_all_deletes_all_tokens(): void
    {
        $user = User::factory()->create();
        $token1 = $user->createToken('token1');
        $token2 = $user->createToken('token2');

        $this->assertEquals(2, $user->tokens()->count());

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token1->plainTextToken,
        ])->postJson('/api/v1/auth/logout-all');

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Logged out from all devices successfully',
            ]);

        // All tokens should be deleted
        $user->refresh();
        $this->assertEquals(0, $user->tokens()->count());
    }

    public function test_logout_requires_authentication(): void
    {
        $response = $this->postJson('/api/v1/auth/logout');

        $response->assertStatus(401);
    }

    public function test_logout_all_requires_authentication(): void
    {
        $response = $this->postJson('/api/v1/auth/logout-all');

        $response->assertStatus(401);
    }

    public function test_logout_with_invalid_token_fails(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
        ])->postJson('/api/v1/auth/logout');

        $response->assertStatus(401);
    }

    public function test_logout_with_expired_token_fails(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token');
        
        // Delete the token to simulate expiration
        $token->accessToken->delete();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson('/api/v1/auth/logout');

        $response->assertStatus(401);
    }

    public function test_user_can_logout_using_sanctum_helper(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/v1/auth/logout');

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Logged out successfully',
            ]);
    }

    public function test_logout_response_format(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token');

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson('/api/v1/auth/logout');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
            ])
            ->assertJson([
                'message' => 'Logged out successfully',
            ]);
    }

    public function test_logout_all_response_format(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token');

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson('/api/v1/auth/logout-all');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
            ])
            ->assertJson([
                'message' => 'Logged out from all devices successfully',
            ]);
    }
}
