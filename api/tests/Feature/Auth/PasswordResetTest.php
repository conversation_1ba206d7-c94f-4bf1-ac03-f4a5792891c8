<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Password;
use Tests\TestCase;

class PasswordResetTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    public function test_user_can_request_password_reset(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Password reset link sent to your email',
            ]);

        Notification::assertSentTo($user, \Illuminate\Auth\Notifications\ResetPassword::class);
    }

    public function test_forgot_password_fails_for_nonexistent_email(): void
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_forgot_password_requires_valid_email_format(): void
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_forgot_password_requires_email(): void
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_user_can_reset_password_with_valid_token(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('old_password'),
        ]);

        // Generate a password reset token
        $token = Password::createToken($user);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'new_password123',
            'password_confirmation' => 'new_password123',
            'token' => $token,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Password reset successfully',
            ]);

        // Verify password was changed
        $user->refresh();
        $this->assertTrue(Hash::check('new_password123', $user->password));

        // Verify all tokens were revoked
        $this->assertEquals(0, $user->tokens()->count());
    }

    public function test_password_reset_fails_with_invalid_token(): void
    {
        User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'new_password123',
            'password_confirmation' => 'new_password123',
            'token' => 'invalid-token',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_password_reset_fails_with_mismatched_email(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $token = Password::createToken($user);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'new_password123',
            'password_confirmation' => 'new_password123',
            'token' => $token,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_password_reset_requires_password_confirmation(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $token = Password::createToken($user);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'new_password123',
            'password_confirmation' => 'different_password',
            'token' => $token,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }

    public function test_password_reset_requires_strong_password(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $token = Password::createToken($user);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => '123',
            'password_confirmation' => '123',
            'token' => $token,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }

    public function test_password_reset_requires_all_fields(): void
    {
        $response = $this->postJson('/api/v1/auth/reset-password', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['token', 'email', 'password']);
    }

    public function test_password_reset_token_can_only_be_used_once(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('old_password'),
        ]);

        $token = Password::createToken($user);

        // First reset should succeed
        $response1 = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'new_password123',
            'password_confirmation' => 'new_password123',
            'token' => $token,
        ]);

        $response1->assertStatus(200);

        // Second reset with same token should fail
        $response2 = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'another_password123',
            'password_confirmation' => 'another_password123',
            'token' => $token,
        ]);

        $response2->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_password_reset_revokes_all_user_tokens(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('old_password'),
        ]);

        // Create some tokens
        $user->createToken('token1');
        $user->createToken('token2');
        $this->assertEquals(2, $user->tokens()->count());

        $token = Password::createToken($user);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'new_password123',
            'password_confirmation' => 'new_password123',
            'token' => $token,
        ]);

        $response->assertStatus(200);

        // All tokens should be revoked
        $user->refresh();
        $this->assertEquals(0, $user->tokens()->count());
    }
}
