<?php

namespace Tests\Feature;

use App\Models\FileUpload;
use App\Models\User;
use App\Services\FileUploadService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class FileControllerTest extends TestCase
{
    use RefreshDatabase;

    private FileUploadService $fileUploadService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->fileUploadService = new FileUploadService();
        Storage::fake('private');
    }

    public function test_authenticated_user_can_access_their_own_files(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/serve/{$encryptedPath}");

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'image/jpeg');
        $response->assertHeader('Content-Disposition', 'inline; filename="test.jpg"');
    }

    public function test_user_cannot_access_other_users_files(): void
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Upload file as user1
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user1, 'avatar');

        // Try to access as user2
        Sanctum::actingAs($user2);
        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/serve/{$encryptedPath}");

        $response->assertStatus(403);
    }

    public function test_unauthenticated_user_cannot_access_files(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/serve/{$encryptedPath}");

        $response->assertStatus(401);
    }

    public function test_serve_returns_404_for_nonexistent_file(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $encryptedPath = encrypt('nonexistent/path.jpg');
        $response = $this->get("/files/serve/{$encryptedPath}");

        $response->assertStatus(404);
    }

    public function test_serve_returns_403_for_infected_files(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        // Mark file as infected
        $fileUpload = FileUpload::where('file_path', $result['file_path'])->first();
        $fileUpload->update(['virus_scan_status' => 'infected']);

        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/serve/{$encryptedPath}");

        $response->assertStatus(403);
        $response->assertSee('File is infected and cannot be served');
    }

    public function test_serve_returns_404_for_invalid_encrypted_path(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->get('/files/serve/invalid-encrypted-path');

        $response->assertStatus(404);
    }

    public function test_download_returns_file_with_attachment_header(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/download/{$encryptedPath}");

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'image/jpeg');
        $response->assertHeader('Content-Disposition', 'attachment; filename="test.jpg"');
    }

    public function test_download_requires_authentication(): void
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/download/{$encryptedPath}");

        $response->assertStatus(401);
    }

    public function test_serve_includes_security_headers(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/serve/{$encryptedPath}");

        $response->assertStatus(200);
        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('Cache-Control', 'private, max-age=3600');
    }

    public function test_serve_handles_missing_file_content(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create file upload record without actual file
        $fileUpload = FileUpload::create([
            'user_id' => $user->id,
            'file_type' => 'avatar',
            'original_name' => 'test.jpg',
            'stored_name' => 'test_stored.jpg',
            'file_path' => 'uploads/users/' . $user->id . '/avatar/test_stored.jpg',
            'file_size' => 2048,
            'mime_type' => 'image/jpeg',
            'virus_scan_status' => 'clean',
            'checksum_md5' => 'test_md5',
            'checksum_sha256' => 'test_sha256',
            'allowed_file_types' => ['jpeg', 'jpg', 'png'],
            'upload_date' => now(),
        ]);

        $encryptedPath = encrypt($fileUpload->file_path);
        $response = $this->get("/files/serve/{$encryptedPath}");

        $response->assertStatus(404);
        $response->assertSee('File content not found');
    }

    public function test_admin_can_access_all_files(): void
    {
        $user = User::factory()->create();
        $admin = User::factory()->create(['role' => 'admin']);

        // Upload file as regular user
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        // Access as admin
        Sanctum::actingAs($admin);
        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/serve/{$encryptedPath}");

        $response->assertStatus(200);
    }

    public function test_serve_returns_correct_content_length(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(2048);
        $result = $this->fileUploadService->uploadFile($file, $user, 'avatar');

        $encryptedPath = encrypt($result['file_path']);
        $response = $this->get("/files/download/{$encryptedPath}");

        $response->assertStatus(200);
        $this->assertNotNull($response->headers->get('Content-Length'));
    }

    public function test_file_paths_are_properly_encrypted(): void
    {
        $filePath = 'uploads/users/1/avatar/test.jpg';
        $encryptedPath = encrypt($filePath);
        
        // Encrypted path should be different from original
        $this->assertNotEquals($filePath, $encryptedPath);
        
        // Should be able to decrypt back to original
        $decryptedPath = decrypt($encryptedPath);
        $this->assertEquals($filePath, $decryptedPath);
    }
}
