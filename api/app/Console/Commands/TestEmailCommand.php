<?php

namespace App\Console\Commands;

use App\Mail\TestEmail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email : The email address to send the test email to} {--message= : Custom test message}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email to verify email configuration';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $email = $this->argument('email');
        $customMessage = $this->option('message') ?? 'This is a test email from Mashora Courses!';

        $this->info('Sending test email...');
        $this->info("To: {$email}");
        $this->info("Message: {$customMessage}");

        try {
            Mail::to($email)->send(new TestEmail($customMessage));

            $this->info('✓ Test email sent successfully!');
            $this->line('');
            $this->info('Email Configuration Details:');
            $this->table(
                ['Setting', 'Value'],
                [
                    ['Mail Driver', config('mail.default')],
                    ['Mail Host', config('mail.mailers.smtp.host')],
                    ['Mail Port', config('mail.mailers.smtp.port')],
                    ['From Address', config('mail.from.address')],
                    ['From Name', config('mail.from.name')],
                ]
            );

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('✗ Failed to send test email!');
            $this->error("Error: {$e->getMessage()}");

            return Command::FAILURE;
        }
    }
}
