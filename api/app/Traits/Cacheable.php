<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;

trait Cacheable
{
    /**
     * Cache key prefix for this model
     */
    protected function getCachePrefix(): string
    {
        return strtolower(class_basename($this)) . '_';
    }

    /**
     * Generate cache key for this model instance
     */
    protected function getCacheKey(string $suffix = ''): string
    {
        $key = $this->getCachePrefix() . $this->getKey();
        return $suffix ? $key . '_' . $suffix : $key;
    }

    /**
     * Generate cache key for model class
     */
    protected static function getClassCacheKey(string $suffix = ''): string
    {
        $prefix = strtolower(class_basename(static::class)) . '_';
        return $suffix ? $prefix . $suffix : $prefix;
    }

    /**
     * Cache a value for this model instance
     */
    protected function cacheValue(string $key, $value, int $ttl = 3600): mixed
    {
        $cacheKey = $this->getCacheKey($key);
        Cache::put($cacheKey, $value, $ttl);
        return $value;
    }

    /**
     * Get cached value for this model instance
     */
    protected function getCachedValue(string $key, callable $callback = null, int $ttl = 3600): mixed
    {
        $cacheKey = $this->getCacheKey($key);
        
        if ($callback) {
            return Cache::remember($cacheKey, $ttl, $callback);
        }
        
        return Cache::get($cacheKey);
    }

    /**
     * Cache a value for the model class
     */
    protected static function cacheClassValue(string $key, $value, int $ttl = 3600): mixed
    {
        $cacheKey = static::getClassCacheKey($key);
        Cache::put($cacheKey, $value, $ttl);
        return $value;
    }

    /**
     * Get cached value for the model class
     */
    protected static function getCachedClassValue(string $key, callable $callback = null, int $ttl = 3600): mixed
    {
        $cacheKey = static::getClassCacheKey($key);
        
        if ($callback) {
            return Cache::remember($cacheKey, $ttl, $callback);
        }
        
        return Cache::get($cacheKey);
    }

    /**
     * Forget cache for this model instance
     */
    protected function forgetCache(string $key = null): void
    {
        if ($key) {
            Cache::forget($this->getCacheKey($key));
        } else {
            // Forget all cache keys for this instance
            $pattern = $this->getCachePrefix() . $this->getKey() . '_*';
            $this->forgetCacheByPattern($pattern);
        }
    }

    /**
     * Forget cache for the model class
     */
    protected static function forgetClassCache(string $key = null): void
    {
        if ($key) {
            Cache::forget(static::getClassCacheKey($key));
        } else {
            // Forget all cache keys for this class
            $pattern = static::getClassCacheKey() . '*';
            static::forgetCacheByPattern($pattern);
        }
    }

    /**
     * Forget cache by pattern (Redis specific)
     */
    protected function forgetCacheByPattern(string $pattern): void
    {
        if (config('cache.default') === 'redis') {
            $redis = Cache::getRedis();
            $keys = $redis->keys($pattern);
            if (!empty($keys)) {
                $redis->del($keys);
            }
        }
    }

    /**
     * Forget cache by pattern (static version)
     */
    protected static function forgetCacheByPattern(string $pattern): void
    {
        if (config('cache.default') === 'redis') {
            $redis = Cache::getRedis();
            $keys = $redis->keys($pattern);
            if (!empty($keys)) {
                $redis->del($keys);
            }
        }
    }

    /**
     * Boot the trait
     */
    protected static function bootCacheable(): void
    {
        // Clear cache when model is updated
        static::updated(function ($model) {
            $model->forgetCache();
        });

        // Clear cache when model is deleted
        static::deleted(function ($model) {
            $model->forgetCache();
        });
    }
}
