<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'church_belong',
        'father_of_confession',
        'confession_church',
        'payment_method',
        'payment_status',
        'registration_step',
    ];

    protected function casts(): array
    {
        return [
            'registration_step' => 'integer',
        ];
    }

    // Relationships
    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
