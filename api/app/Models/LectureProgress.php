<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LectureProgress extends Model
{
    use HasFactory;

    protected $table = 'lecture_progress';

    protected $fillable = [
        'user_id',
        'lecture_id',
        'watch_time',
        'completion_percentage',
        'is_completed',
        'started_at',
        'completed_at',
        'bookmark_seconds',
    ];

    protected function casts(): array
    {
        return [
            'watch_time' => 'integer',
            'completion_percentage' => 'integer',
            'is_completed' => 'boolean',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'bookmark_seconds' => 'integer',
        ];
    }

    // Relationships
    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function lecture(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Lecture::class);
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    public function scopeInProgress($query)
    {
        return $query->where('is_completed', false)->where('watch_time', '>', 0);
    }

    public function scopeNotStarted($query)
    {
        return $query->where('watch_time', 0);
    }

    public function scopeWithLecture($query)
    {
        return $query->with('lecture');
    }

    public function scopeWithUser($query)
    {
        return $query->with('user');
    }

    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByLecture($query, int $lectureId)
    {
        return $query->where('lecture_id', $lectureId);
    }

    public function scopeByCompletionPercentage($query, int $minPercentage)
    {
        return $query->where('completion_percentage', '>=', $minPercentage);
    }

    public function scopeRecentlyUpdated($query, int $days = 7)
    {
        return $query->where('updated_at', '>=', now()->subDays($days));
    }

    public function scopeOrderedByProgress($query, string $direction = 'desc')
    {
        return $query->orderBy('completion_percentage', $direction);
    }
}
