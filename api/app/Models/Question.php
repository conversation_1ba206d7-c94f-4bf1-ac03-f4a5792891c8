<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;

    protected $fillable = [
        'quiz_id',
        'text',
        'text_ar',
        'options_json',
        'options_json_ar',
        'correct_answer',
        'explanation',
        'explanation_ar',
        'difficulty',
    ];

    protected function casts(): array
    {
        return [
            'options_json' => 'array',
            'options_json_ar' => 'array',
        ];
    }

    // Relationships
    public function quiz(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    // Methods
    public function getTextAttribute($value): string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->text_ar ? $this->text_ar : $value;
    }

    public function getOptionsAttribute(): array
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->options_json_ar ? $this->options_json_ar : $this->options_json;
    }

    public function getExplanationAttribute($value): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->explanation_ar ? $this->explanation_ar : $value;
    }
}
