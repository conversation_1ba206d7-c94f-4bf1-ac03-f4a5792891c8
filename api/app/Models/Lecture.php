<?php

namespace App\Models;

use App\Traits\Cacheable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Lecture extends Model
{
    use HasFactory, Cacheable;

    protected $fillable = [
        'title',
        'title_en',
        'title_ar',
        'duration',
        'description',
        'description_ar',
        'video_url',
        'transcript',
        'transcript_ar',
        'order_index',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'duration' => 'integer',
            'order_index' => 'integer',
        ];
    }

    // Relationships
    public function quiz(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Quiz::class);
    }

    public function progress(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(LectureProgress::class);
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order_index');
    }

    public function scopeWithQuiz($query)
    {
        return $query->with(['quiz' => function ($query) {
            $query->with('questions');
        }]);
    }

    public function scopeWithProgress($query)
    {
        return $query->with('progress');
    }

    public function scopeWithUserProgress($query, int $userId)
    {
        return $query->with(['progress' => function ($query) use ($userId) {
            $query->where('user_id', $userId);
        }]);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeOrderedByIndex($query, string $direction = 'asc')
    {
        return $query->orderBy('order_index', $direction);
    }

    public function scopeCreatedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    // Methods
    public function getTitleAttribute($value): string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->title_ar ? $this->title_ar : $value;
    }

    public function getDescriptionAttribute($value): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->description_ar ? $this->description_ar : $value;
    }

    public function getTranscriptAttribute($value): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && $this->transcript_ar ? $this->transcript_ar : $value;
    }

    // Cached methods for performance
    public function getCachedProgressStatistics(): array
    {
        return $this->getCachedValue('progress_statistics', function () {
            $totalUsers = User::active()->count();
            $usersStarted = $this->progress()->distinct('user_id')->count();
            $usersCompleted = $this->progress()->completed()->distinct('user_id')->count();
            $averageCompletion = $this->progress()->avg('completion_percentage') ?? 0;
            $totalWatchTime = $this->progress()->sum('watch_time');

            return [
                'total_users' => $totalUsers,
                'users_started' => $usersStarted,
                'users_completed' => $usersCompleted,
                'completion_rate' => $usersStarted > 0 ? round(($usersCompleted / $usersStarted) * 100, 2) : 0,
                'average_completion_percentage' => round($averageCompletion, 2),
                'total_watch_time' => $totalWatchTime,
                'average_watch_time' => $usersStarted > 0 ? round($totalWatchTime / $usersStarted, 2) : 0,
            ];
        }, 1800); // Cache for 30 minutes
    }

    public function getCachedQuizData(): ?Quiz
    {
        return $this->getCachedValue('quiz_data', function () {
            return $this->quiz()->with('questions')->first();
        }, 3600); // Cache for 1 hour
    }

    public static function getCachedPublishedLectures(): \Illuminate\Database\Eloquent\Collection
    {
        return static::getCachedClassValue('published_lectures', function () {
            return static::published()->ordered()->get();
        }, 3600); // Cache for 1 hour
    }
}
