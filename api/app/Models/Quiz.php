<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Quiz extends Model
{
    use HasFactory;

    protected $fillable = [
        'lecture_id',
        'title',
        'time_limit',
        'passing_score',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'time_limit' => 'integer',
            'passing_score' => 'integer',
        ];
    }

    // Relationships
    public function lecture(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Lecture::class);
    }

    public function questions(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Question::class);
    }

    public function attempts(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(QuizAttempt::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeWithQuestions($query)
    {
        return $query->with('questions');
    }

    public function scopeWithAttempts($query)
    {
        return $query->with(['attempts' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }]);
    }

    public function scopeWithUserAttempts($query, int $userId)
    {
        return $query->with(['attempts' => function ($query) use ($userId) {
            $query->where('user_id', $userId)
                  ->orderBy('attempt_no', 'desc');
        }]);
    }

    public function scopeByLecture($query, int $lectureId)
    {
        return $query->where('lecture_id', $lectureId);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeWithPassingScore($query, int $minScore)
    {
        return $query->where('passing_score', '>=', $minScore);
    }
}
