<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'quiz_id',
        'answers_json',
        'score',
        'passed',
        'attempt_no',
        'started_at',
        'completed_at',
    ];

    protected function casts(): array
    {
        return [
            'answers_json' => 'array',
            'score' => 'integer',
            'passed' => 'boolean',
            'attempt_no' => 'integer',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
        ];
    }

    // Relationships
    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function quiz(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    // Scopes
    public function scopePassed($query)
    {
        return $query->where('passed', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('passed', false);
    }

    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    public function scopeInProgress($query)
    {
        return $query->whereNull('completed_at');
    }

    public function scopeWithQuiz($query)
    {
        return $query->with(['quiz' => function ($query) {
            $query->with('lecture');
        }]);
    }

    public function scopeWithUser($query)
    {
        return $query->with('user');
    }

    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByQuiz($query, int $quizId)
    {
        return $query->where('quiz_id', $quizId);
    }

    public function scopeByScore($query, int $minScore)
    {
        return $query->where('score', '>=', $minScore);
    }

    public function scopeLatestAttempts($query)
    {
        return $query->orderBy('attempt_no', 'desc');
    }

    public function scopeRecentAttempts($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

}
