<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FinalExamAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'answers_json',
        'score',
        'passed',
        'attempt_no',
        'confidence',
        'flagged',
        'started_at',
        'completed_at',
    ];

    protected function casts(): array
    {
        return [
            'answers_json' => 'array',
            'confidence' => 'array',
            'flagged' => 'array',
            'score' => 'integer',
            'passed' => 'boolean',
            'attempt_no' => 'integer',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
        ];
    }

    // Relationships
    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopePassed($query)
    {
        return $query->where('passed', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('passed', false);
    }

    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }
}
