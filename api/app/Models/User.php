<?php

namespace App\Models;

use App\Traits\Cacheable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, Cacheable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'email',
        'phone',
        'password',
        'first_name',
        'last_name',
        'national_id',
        'date_of_birth',
        'address',
        'avatar_url',
        'avatar_file_path',
        'national_id_front_file_path',
        'national_id_back_file_path',
        'identity_verification_status',
        'identity_verification_notes',
        'identity_verified_at',
        'status',
        'email_verified',
        'email_verified_at',
        'last_login_at',
        'login_count',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'date_of_birth' => 'date',
            'deleted_at' => 'datetime',
            'last_login_at' => 'datetime',
            'identity_verified_at' => 'datetime',
            'password' => 'hashed',
            'email_verified' => 'boolean',
        ];
    }

    // Relationships
    public function profile(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(UserProfile::class);
    }

    public function progress(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(LectureProgress::class);
    }

    public function quizAttempts(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(QuizAttempt::class);
    }

    public function finalAttempts(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(FinalExamAttempt::class);
    }

    public function certificates(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Certificate::class);
    }

    public function payments(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    public function receipts(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(PaymentReceipt::class);
    }

    public function uploads(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(FileUpload::class);
    }

    public function auditLogs(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(AuditLog::class);
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function getIsAdminAttribute(): bool
    {
        return $this->role === 'admin';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeVerified($query)
    {
        return $query->where('email_verified', true);
    }

    public function scopeIdentityVerified($query)
    {
        return $query->where('identity_verification_status', 'verified');
    }

    public function scopeWithProfile($query)
    {
        return $query->with('profile');
    }

    public function scopeWithProgress($query)
    {
        return $query->with(['progress' => function ($query) {
            $query->with('lecture')->orderBy('created_at', 'desc');
        }]);
    }

    public function scopeWithLatestProgress($query, int $limit = 5)
    {
        return $query->with(['progress' => function ($query) use ($limit) {
            $query->with('lecture')
                  ->orderBy('updated_at', 'desc')
                  ->limit($limit);
        }]);
    }

    public function scopeWithCertificates($query)
    {
        return $query->with(['certificates' => function ($query) {
            $query->orderBy('issue_date', 'desc');
        }]);
    }

    public function scopeWithPayments($query)
    {
        return $query->with(['payments' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }]);
    }

    public function scopeRecentlyActive($query, int $days = 30)
    {
        return $query->where('last_login_at', '>=', now()->subDays($days));
    }

    public function scopeRegisteredBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByIdentityStatus($query, string $status)
    {
        return $query->where('identity_verification_status', $status);
    }

    // Methods
    public function canAccessLecture($lectureId): bool
    {
        // Check if user has completed previous lecture
        $lecture = Lecture::find($lectureId);
        if (!$lecture || $lecture->order_index === 0) {
            return true;
        }

        $previousLecture = Lecture::where('order_index', $lecture->order_index - 1)->first();
        if (!$previousLecture) {
            return true;
        }

        return $this->progress()
            ->where('lecture_id', $previousLecture->id)
            ->where('is_completed', true)
            ->exists();
    }

    public function hasPaid(): bool
    {
        return $this->payments()
            ->where('status', 'completed')
            ->exists();
    }

    public function isEligibleForCertificate(): bool
    {
        return $this->hasPaid() &&
               $this->progress()->where('is_completed', true)->count() === Lecture::count() &&
               $this->finalAttempts()->where('passed', true)->exists();
    }

    public function updateLoginInfo(): void
    {
        $this->update([
            'last_login_at' => now(),
            'login_count' => $this->login_count + 1,
        ]);
    }

    // Email verification methods
    public function hasVerifiedEmail(): bool
    {
        return (bool) $this->email_verified;
    }

    public function getEmailForVerification(): string
    {
        return $this->email;
    }

    public function markEmailAsVerified(): bool
    {
        $this->email_verified = true;
        $this->email_verified_at = now();
        return $this->save();
    }

    public function sendEmailVerificationNotification(): void
    {
        $this->notify(new \App\Notifications\VerifyEmailNotification);
    }

    // File upload relationships
    public function avatarFile(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(FileUpload::class, 'file_path', 'avatar_file_path');
    }

    public function nationalIdFrontFile(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(FileUpload::class, 'file_path', 'national_id_front_file_path');
    }

    public function nationalIdBackFile(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(FileUpload::class, 'file_path', 'national_id_back_file_path');
    }

    // Identity verification methods
    public function isIdentityVerified(): bool
    {
        return $this->identity_verification_status === 'verified';
    }

    public function hasRequiredIdentityDocuments(): bool
    {
        return !empty($this->national_id_front_file_path) && !empty($this->national_id_back_file_path);
    }

    public function markIdentityAsVerified(string $notes = null): bool
    {
        return $this->update([
            'identity_verification_status' => 'verified',
            'identity_verification_notes' => $notes,
            'identity_verified_at' => now(),
        ]);
    }

    public function markIdentityAsRejected(string $notes): bool
    {
        return $this->update([
            'identity_verification_status' => 'rejected',
            'identity_verification_notes' => $notes,
            'identity_verified_at' => null,
        ]);
    }

    // File URL accessors
    public function getAvatarUrlAttribute($value): ?string
    {
        if ($this->avatar_file_path) {
            return asset('storage/' . $this->avatar_file_path);
        }
        return $value;
    }

    // Cached methods for performance
    public function getCachedProgressSummary(): array
    {
        return $this->getCachedValue('progress_summary', function () {
            $totalLectures = Lecture::published()->count();
            $completedLectures = $this->progress()->completed()->count();
            $inProgressLectures = $this->progress()->inProgress()->count();
            $totalWatchTime = $this->progress()->sum('watch_time');

            return [
                'total_lectures' => $totalLectures,
                'completed_lectures' => $completedLectures,
                'in_progress_lectures' => $inProgressLectures,
                'completion_percentage' => $totalLectures > 0 ? round(($completedLectures / $totalLectures) * 100, 2) : 0,
                'total_watch_time' => $totalWatchTime,
                'total_watch_time_formatted' => gmdate('H:i:s', $totalWatchTime),
            ];
        }, 1800); // Cache for 30 minutes
    }

    public function getCachedQuizStatistics(): array
    {
        return $this->getCachedValue('quiz_statistics', function () {
            $totalAttempts = $this->quizAttempts()->count();
            $passedAttempts = $this->quizAttempts()->passed()->count();
            $averageScore = $this->quizAttempts()->avg('score') ?? 0;
            $bestScore = $this->quizAttempts()->max('score') ?? 0;

            return [
                'total_attempts' => $totalAttempts,
                'passed_attempts' => $passedAttempts,
                'pass_rate' => $totalAttempts > 0 ? round(($passedAttempts / $totalAttempts) * 100, 2) : 0,
                'average_score' => round($averageScore, 2),
                'best_score' => $bestScore,
            ];
        }, 1800); // Cache for 30 minutes
    }

    public function getCachedCertificates(): \Illuminate\Database\Eloquent\Collection
    {
        return $this->getCachedValue('certificates', function () {
            return $this->certificates()->orderBy('issue_date', 'desc')->get();
        }, 3600); // Cache for 1 hour
    }

    public function getCachedPaymentHistory(): \Illuminate\Database\Eloquent\Collection
    {
        return $this->getCachedValue('payment_history', function () {
            return $this->payments()
                ->with('receipt')
                ->orderBy('created_at', 'desc')
                ->get();
        }, 1800); // Cache for 30 minutes
    }

    public function getNationalIdFrontUrlAttribute(): ?string
    {
        return $this->national_id_front_file_path ? asset('storage/' . $this->national_id_front_file_path) : null;
    }

    public function getNationalIdBackUrlAttribute(): ?string
    {
        return $this->national_id_back_file_path ? asset('storage/' . $this->national_id_back_file_path) : null;
    }
}
