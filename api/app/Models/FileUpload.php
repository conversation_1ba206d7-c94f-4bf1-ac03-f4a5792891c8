<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FileUpload extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'file_type',
        'original_name',
        'stored_name',
        'file_path',
        'file_size',
        'mime_type',
        'virus_scan_status',
        'virus_scan_result',
        'checksum_md5',
        'checksum_sha256',
        'allowed_file_types',
        'upload_date',
    ];

    protected function casts(): array
    {
        return [
            'file_size' => 'integer',
            'virus_scan_result' => 'array',
            'allowed_file_types' => 'array',
            'upload_date' => 'datetime',
        ];
    }

    // Relationships
    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeClean($query)
    {
        return $query->where('virus_scan_status', 'clean');
    }

    public function scopeInfected($query)
    {
        return $query->where('virus_scan_status', 'infected');
    }

    public function scopePendingScan($query)
    {
        return $query->where('virus_scan_status', 'pending');
    }
}
