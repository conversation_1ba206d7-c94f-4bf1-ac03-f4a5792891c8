<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Certificate extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'certificate_id',
        'grade',
        'grade_percentage',
        'issue_date',
        'validation_code',
        'qr_code_url',
        'pdf_url',
    ];

    protected function casts(): array
    {
        return [
            'grade_percentage' => 'integer',
            'issue_date' => 'datetime',
        ];
    }

    // Relationships
    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeByValidationCode($query, $code)
    {
        return $query->where('validation_code', $code);
    }

    public function scopeByCertificateId($query, $certificateId)
    {
        return $query->where('certificate_id', $certificateId);
    }
}
