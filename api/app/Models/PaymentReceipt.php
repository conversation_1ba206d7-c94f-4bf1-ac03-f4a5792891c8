<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentReceipt extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'transaction_id',
        'receipt_number',
        'amount',
        'currency',
        'payment_method',
        'card_last4',
        'card_type',
        'status',
        'subscription_type',
        'fees_included',
        'student_name',
        'course_name',
        'course_name_ar',
        'fees_description',
        'transaction_date',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'fees_included' => 'boolean',
            'transaction_date' => 'datetime',
        ];
    }

    // Relationships
    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transaction(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(PaymentTransaction::class, 'transaction_id', 'transaction_id');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeByReceiptNumber($query, $receiptNumber)
    {
        return $query->where('receipt_number', $receiptNumber);
    }
}
