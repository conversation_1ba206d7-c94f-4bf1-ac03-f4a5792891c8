<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;

class RateLimitServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        RateLimiter::for('auth', function (Request $request) {
            return [
                // 5 attempts per minute per IP
                Limit::perMinute(5)->by($request->ip()),
                // 10 attempts per minute per email (for login attempts)
                Limit::perMinute(10)->by($request->input('email')),
            ];
        });

        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        RateLimiter::for('password-reset', function (Request $request) {
            return [
                // 3 password reset attempts per hour per IP
                Limit::perHour(3)->by($request->ip()),
                // 5 password reset attempts per hour per email
                Limit::perHour(5)->by($request->input('email')),
            ];
        });

        RateLimiter::for('email-verification', function (Request $request) {
            return [
                // 3 verification emails per hour per IP
                Limit::perHour(3)->by($request->ip()),
                // 5 verification emails per hour per email
                Limit::perHour(5)->by($request->input('email')),
            ];
        });
    }
}
