<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Http\UploadedFile;

class SecureImageUpload implements ValidationRule
{
    private const ALLOWED_MIME_TYPES = ['image/jpeg', 'image/jpg', 'image/png'];
    private const ALLOWED_EXTENSIONS = ['jpeg', 'jpg', 'png'];
    private const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    private const MIN_DIMENSIONS = 200; // 200x200 minimum
    private const MAX_DIMENSIONS = 2048; // 2048x2048 maximum

    public function __construct(
        private string $fileType = 'image',
        private bool $requireMinDimensions = true
    ) {}

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$value instanceof UploadedFile) {
            $fail('The :attribute must be a valid file.');
            return;
        }

        // Check if file was uploaded successfully
        if (!$value->isValid()) {
            $fail('The :attribute upload failed. Please try again.');
            return;
        }

        // Check file size
        if ($value->getSize() > self::MAX_FILE_SIZE) {
            $fail('The :attribute must not be larger than 5MB.');
            return;
        }

        // Check MIME type
        if (!in_array($value->getMimeType(), self::ALLOWED_MIME_TYPES)) {
            $fail('The :attribute must be a JPEG or PNG image.');
            return;
        }

        // Check file extension
        $extension = strtolower($value->getClientOriginalExtension());
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            $fail('The :attribute must have a valid image extension (jpeg, jpg, png).');
            return;
        }

        // Validate image content
        $imageInfo = @getimagesize($value->getPathname());
        if (!$imageInfo) {
            $fail('The :attribute must be a valid image file.');
            return;
        }

        [$width, $height, $type] = $imageInfo;

        // Check image type matches MIME type
        $allowedImageTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG];
        if (!in_array($type, $allowedImageTypes)) {
            $fail('The :attribute must be a valid JPEG or PNG image.');
            return;
        }

        // Check dimensions
        if ($this->requireMinDimensions) {
            if ($width < self::MIN_DIMENSIONS || $height < self::MIN_DIMENSIONS) {
                $fail("The :attribute must be at least " . self::MIN_DIMENSIONS . "x" . self::MIN_DIMENSIONS . " pixels.");
                return;
            }
        }

        if ($width > self::MAX_DIMENSIONS || $height > self::MAX_DIMENSIONS) {
            $fail("The :attribute must not exceed " . self::MAX_DIMENSIONS . "x" . self::MAX_DIMENSIONS . " pixels.");
            return;
        }

        // Additional security checks
        $this->performSecurityChecks($value, $fail);
    }

    /**
     * Perform additional security checks
     */
    private function performSecurityChecks(UploadedFile $file, Closure $fail): void
    {
        // Check for suspicious file content
        $content = file_get_contents($file->getPathname());
        
        // Look for PHP tags or other suspicious content
        $suspiciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec/i',
            '/passthru/i',
            '/base64_decode/i',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $fail('The :attribute contains suspicious content and cannot be uploaded.');
                return;
            }
        }

        // Check file header matches extension
        $realMimeType = mime_content_type($file->getPathname());
        if (!in_array($realMimeType, self::ALLOWED_MIME_TYPES)) {
            $fail('The :attribute file type does not match its content.');
            return;
        }

        // Check for double extensions
        $filename = $file->getClientOriginalName();
        if (preg_match('/\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)\./', $filename)) {
            $fail('The :attribute filename contains suspicious extensions.');
            return;
        }
    }

    /**
     * Create rule for avatar images
     */
    public static function avatar(): self
    {
        return new self('avatar', false); // Avatar doesn't require minimum dimensions
    }

    /**
     * Create rule for national ID images
     */
    public static function nationalId(): self
    {
        return new self('national_id', true); // National ID requires minimum dimensions
    }
}
