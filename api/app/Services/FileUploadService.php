<?php

namespace App\Services;

use App\Models\FileUpload;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadService
{
    private const ALLOWED_IMAGE_TYPES = ['jpeg', 'jpg', 'png'];
    private const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    private const MAX_IMAGE_WIDTH = 2048;
    private const MAX_IMAGE_HEIGHT = 2048;
    private const MIN_IMAGE_WIDTH = 200;
    private const MIN_IMAGE_HEIGHT = 200;

    /**
     * Upload and process a file
     */
    public function uploadFile(UploadedFile $file, User $user, string $fileType): array
    {
        // Validate file
        $this->validateFile($file, $fileType);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file, $fileType);
        
        // Determine storage path
        $storagePath = $this->getStoragePath($user->id, $fileType);
        $fullPath = $storagePath . '/' . $filename;

        // Process image if needed
        if ($this->isImageFile($file)) {
            $processedImage = $this->processImage($file, $fileType);
            Storage::disk('private')->put($fullPath, $processedImage);
        } else {
            Storage::disk('private')->putFileAs($storagePath, $file, $filename);
        }

        // Calculate checksums
        $fileContent = Storage::disk('private')->get($fullPath);
        $md5Hash = md5($fileContent);
        $sha256Hash = hash('sha256', $fileContent);

        // Create file upload record
        $fileUpload = FileUpload::create([
            'user_id' => $user->id,
            'file_type' => $fileType,
            'original_name' => $file->getClientOriginalName(),
            'stored_name' => $filename,
            'file_path' => $fullPath,
            'file_size' => Storage::disk('private')->size($fullPath),
            'mime_type' => $file->getMimeType(),
            'virus_scan_status' => 'pending',
            'checksum_md5' => $md5Hash,
            'checksum_sha256' => $sha256Hash,
            'allowed_file_types' => self::ALLOWED_IMAGE_TYPES,
            'upload_date' => now(),
        ]);

        // Queue virus scan (placeholder for now)
        $this->queueVirusScan($fileUpload);

        return [
            'file_upload' => $fileUpload,
            'file_path' => $fullPath,
            'file_url' => $this->getFileUrl($fullPath),
        ];
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file, string $fileType): void
    {
        // Check file size
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            throw new \InvalidArgumentException('File size exceeds maximum allowed size of 5MB');
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, self::ALLOWED_IMAGE_TYPES)) {
            throw new \InvalidArgumentException('Invalid file type. Only JPEG and PNG files are allowed');
        }

        // Check MIME type
        $mimeType = $file->getMimeType();
        $allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            throw new \InvalidArgumentException('Invalid MIME type. Only JPEG and PNG images are allowed');
        }

        // Validate image dimensions for identity documents
        if (in_array($fileType, ['national_id_front', 'national_id_back'])) {
            $this->validateImageDimensions($file);
        }
    }

    /**
     * Validate image dimensions
     */
    private function validateImageDimensions(UploadedFile $file): void
    {
        $imageInfo = getimagesize($file->getPathname());
        if (!$imageInfo) {
            throw new \InvalidArgumentException('Invalid image file');
        }

        [$width, $height] = $imageInfo;

        if ($width < self::MIN_IMAGE_WIDTH || $height < self::MIN_IMAGE_HEIGHT) {
            throw new \InvalidArgumentException(
                sprintf('Image dimensions too small. Minimum size is %dx%d pixels', 
                    self::MIN_IMAGE_WIDTH, self::MIN_IMAGE_HEIGHT)
            );
        }

        if ($width > self::MAX_IMAGE_WIDTH || $height > self::MAX_IMAGE_HEIGHT) {
            throw new \InvalidArgumentException(
                sprintf('Image dimensions too large. Maximum size is %dx%d pixels', 
                    self::MAX_IMAGE_WIDTH, self::MAX_IMAGE_HEIGHT)
            );
        }
    }

    /**
     * Process image (resize, optimize)
     */
    private function processImage(UploadedFile $file, string $fileType): string
    {
        $image = Image::make($file->getPathname());

        // Resize if too large
        if ($image->width() > self::MAX_IMAGE_WIDTH || $image->height() > self::MAX_IMAGE_HEIGHT) {
            $image->resize(self::MAX_IMAGE_WIDTH, self::MAX_IMAGE_HEIGHT, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }

        // Optimize quality
        $quality = $fileType === 'avatar' ? 85 : 90; // Higher quality for ID documents
        
        return $image->encode('jpg', $quality)->__toString();
    }

    /**
     * Generate unique filename
     */
    private function generateUniqueFilename(UploadedFile $file, string $fileType): string
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        
        return "{$fileType}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Get storage path for file type
     */
    private function getStoragePath(int $userId, string $fileType): string
    {
        return "uploads/users/{$userId}/{$fileType}";
    }

    /**
     * Check if file is an image
     */
    private function isImageFile(UploadedFile $file): bool
    {
        return str_starts_with($file->getMimeType(), 'image/');
    }

    /**
     * Get file URL for serving
     */
    private function getFileUrl(string $filePath): string
    {
        return route('file.serve', ['path' => encrypt($filePath)]);
    }

    /**
     * Queue virus scan (placeholder)
     */
    private function queueVirusScan(FileUpload $fileUpload): void
    {
        // For now, mark as clean. In production, integrate with virus scanning service
        $fileUpload->update([
            'virus_scan_status' => 'clean',
            'virus_scan_result' => [
                'scanned_at' => now()->toISOString(),
                'scanner' => 'placeholder',
                'result' => 'clean'
            ]
        ]);
    }

    /**
     * Delete file and its record
     */
    public function deleteFile(string $filePath): bool
    {
        $fileUpload = FileUpload::where('file_path', $filePath)->first();
        
        if ($fileUpload) {
            // Delete physical file
            if (Storage::disk('private')->exists($filePath)) {
                Storage::disk('private')->delete($filePath);
            }
            
            // Delete database record
            $fileUpload->delete();
            
            return true;
        }
        
        return false;
    }

    /**
     * Get file content for serving
     */
    public function getFileContent(string $filePath): ?string
    {
        if (Storage::disk('private')->exists($filePath)) {
            return Storage::disk('private')->get($filePath);
        }
        
        return null;
    }

    /**
     * Get file MIME type
     */
    public function getFileMimeType(string $filePath): ?string
    {
        $fileUpload = FileUpload::where('file_path', $filePath)->first();
        return $fileUpload?->mime_type;
    }
}
