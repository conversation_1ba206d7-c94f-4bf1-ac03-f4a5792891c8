<?php

namespace App\Http\Requests\Auth;

use App\Rules\SecureImageUpload;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'max:20', 'unique:users'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'national_id' => ['nullable', 'string', 'max:20', 'unique:users'],
            'date_of_birth' => ['nullable', 'date', 'before:today'],
            'address' => ['nullable', 'string', 'max:500'],
            'church_belong' => ['nullable', 'string', 'max:255'],
            'father_of_confession' => ['nullable', 'string', 'max:255'],
            'confession_church' => ['nullable', 'string', 'max:255'],

            // File upload fields
            'avatar' => ['nullable', 'file', SecureImageUpload::avatar()],
            'national_id_front' => ['required', 'file', SecureImageUpload::nationalId()],
            'national_id_back' => ['required', 'file', SecureImageUpload::nationalId()],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required',
            'last_name.required' => 'Last name is required',
            'email.required' => 'Email address is required',
            'email.email' => 'Please provide a valid email address',
            'email.unique' => 'This email address is already registered',
            'phone.unique' => 'This phone number is already registered',
            'password.required' => 'Password is required',
            'password.confirmed' => 'Password confirmation does not match',
            'national_id.unique' => 'This national ID is already registered',
            'date_of_birth.before' => 'Date of birth must be before today',

            // File upload messages
            'avatar.file' => 'Avatar must be a valid file',
            'national_id_front.required' => 'National ID front image is required',
            'national_id_front.file' => 'National ID front must be a valid image file',
            'national_id_back.required' => 'National ID back image is required',
            'national_id_back.file' => 'National ID back must be a valid image file',
        ];
    }
}
