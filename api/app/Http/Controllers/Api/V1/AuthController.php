<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Auth\ResendVerificationRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Jobs\SendWelcomeEmail;
use App\Services\FileUploadService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    public function __construct(
        private FileUploadService $fileUploadService
    ) {}

    /**
     * Register a new user
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        try {
            // Create user first
            $user = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => $request->password,
                'national_id' => $request->national_id,
                'date_of_birth' => $request->date_of_birth,
                'address' => $request->address,
                'identity_verification_status' => 'pending',
            ]);

            // Handle file uploads
            $filePaths = [];

            // Upload avatar if provided
            if ($request->hasFile('avatar')) {
                $avatarUpload = $this->fileUploadService->uploadFile(
                    $request->file('avatar'),
                    $user,
                    'avatar'
                );
                $filePaths['avatar_file_path'] = $avatarUpload['file_path'];
            }

            // Upload national ID front (required)
            if ($request->hasFile('national_id_front')) {
                $frontUpload = $this->fileUploadService->uploadFile(
                    $request->file('national_id_front'),
                    $user,
                    'national_id_front'
                );
                $filePaths['national_id_front_file_path'] = $frontUpload['file_path'];
            }

            // Upload national ID back (required)
            if ($request->hasFile('national_id_back')) {
                $backUpload = $this->fileUploadService->uploadFile(
                    $request->file('national_id_back'),
                    $user,
                    'national_id_back'
                );
                $filePaths['national_id_back_file_path'] = $backUpload['file_path'];
            }

            // Update user with file paths
            if (!empty($filePaths)) {
                $user->update($filePaths);
            }

            // Create user profile
            $user->profile()->create([
                'church_belong' => $request->church_belong,
                'father_of_confession' => $request->father_of_confession,
                'confession_church' => $request->confession_church,
                'registration_step' => 2, // Advanced to step 2 with file uploads
            ]);

            // Fire registered event for email verification
            event(new Registered($user));

            // Send welcome email
            SendWelcomeEmail::dispatch($user);

            // Create token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'message' => 'Registration successful. Please check your email to verify your account.',
                'user' => new UserResource($user->load('profile')),
                'token' => $token,
                'token_type' => 'Bearer',
            ], 201);

        } catch (\Exception $e) {
            // If user was created but file upload failed, clean up
            if (isset($user)) {
                $user->delete();
            }

            return response()->json([
                'message' => 'Registration failed: ' . $e->getMessage(),
                'errors' => ['general' => ['File upload failed. Please try again.']],
            ], 422);
        }
    }

    /**
     * Login user
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->only('email', 'password');

        if (!Auth::attempt($credentials)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $user = Auth::user();

        // Check if user is active
        if ($user->status !== 'active') {
            Auth::logout();
            throw ValidationException::withMessages([
                'email' => ['Your account has been suspended. Please contact support.'],
            ]);
        }

        // Update login information
        $user->updateLoginInfo();

        // Create token
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'message' => 'Login successful',
            'user' => new UserResource($user->load('profile')),
            'token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Logged out successfully',
        ]);
    }

    /**
     * Logout from all devices
     */
    public function logoutAll(Request $request): JsonResponse
    {
        $request->user()->tokens()->delete();

        return response()->json([
            'message' => 'Logged out from all devices successfully',
        ]);
    }

    /**
     * Send password reset link
     */
    public function forgotPassword(ForgotPasswordRequest $request): JsonResponse
    {
        $status = Password::sendResetLink(
            $request->only('email')
        );

        if ($status === Password::RESET_LINK_SENT) {
            return response()->json([
                'message' => 'Password reset link sent to your email',
            ]);
        }

        throw ValidationException::withMessages([
            'email' => [__($status)],
        ]);
    }

    /**
     * Reset password
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => $password,
                ])->setRememberToken(Str::random(60));

                $user->save();

                // Revoke all tokens
                $user->tokens()->delete();
            }
        );

        if ($status === Password::PASSWORD_RESET) {
            return response()->json([
                'message' => 'Password reset successfully',
            ]);
        }

        throw ValidationException::withMessages([
            'email' => [__($status)],
        ]);
    }

    /**
     * Verify email
     */
    public function verifyEmail(Request $request): JsonResponse
    {
        $user = User::find($request->route('id'));

        if (!$user) {
            return response()->json([
                'message' => 'Invalid verification link',
            ], 400);
        }

        if (!hash_equals((string) $request->route('hash'), sha1($user->getEmailForVerification()))) {
            return response()->json([
                'message' => 'Invalid verification link',
            ], 400);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'message' => 'Email already verified',
            ]);
        }

        $user->email_verified = true;
        $user->email_verified_at = now();
        $user->save();

        event(new Verified($user));

        return response()->json([
            'message' => 'Email verified successfully',
        ]);
    }

    /**
     * Resend email verification
     */
    public function resendVerification(ResendVerificationRequest $request): JsonResponse
    {
        $user = User::where('email', $request->email)->first();

        if (!$user) {
            throw ValidationException::withMessages([
                'email' => ['User not found'],
            ]);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'message' => 'Email already verified',
            ]);
        }

        $user->sendEmailVerificationNotification();

        return response()->json([
            'message' => 'Verification email sent',
        ]);
    }

    /**
     * Refresh token
     */
    public function refresh(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Revoke current token
        $request->user()->currentAccessToken()->delete();
        
        // Create new token
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'token' => $token,
            'token_type' => 'Bearer',
            'user' => new UserResource($user->load('profile')),
        ]);
    }

    /**
     * Get authenticated user
     */
    public function me(Request $request): JsonResponse
    {
        return response()->json([
            'user' => new UserResource($request->user()->load('profile')),
        ]);
    }
}
