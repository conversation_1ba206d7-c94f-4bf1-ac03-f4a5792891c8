<?php

namespace App\Http\Controllers;

use App\Models\FileUpload;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class FileController extends Controller
{
    public function __construct(
        private FileUploadService $fileUploadService
    ) {}

    /**
     * Serve a file securely
     */
    public function serve(Request $request, string $path): Response
    {
        try {
            // Decrypt the file path
            $filePath = decrypt($path);
            
            // Get file upload record
            $fileUpload = FileUpload::where('file_path', $filePath)->first();
            
            if (!$fileUpload) {
                abort(404, 'File not found');
            }

            // Check if user has permission to access this file
            if (!$this->canAccessFile($fileUpload)) {
                abort(403, 'Access denied');
            }

            // Check virus scan status
            if ($fileUpload->virus_scan_status === 'infected') {
                abort(403, 'File is infected and cannot be served');
            }

            // Get file content
            $content = $this->fileUploadService->getFileContent($filePath);
            
            if (!$content) {
                abort(404, 'File content not found');
            }

            // Return file response
            return response($content)
                ->header('Content-Type', $fileUpload->mime_type)
                ->header('Content-Disposition', 'inline; filename="' . $fileUpload->original_name . '"')
                ->header('Cache-Control', 'private, max-age=3600')
                ->header('X-Content-Type-Options', 'nosniff');
                
        } catch (\Exception $e) {
            abort(404, 'Invalid file path');
        }
    }

    /**
     * Check if current user can access the file
     */
    private function canAccessFile(FileUpload $fileUpload): bool
    {
        $user = Auth::user();
        
        // User must be authenticated
        if (!$user) {
            return false;
        }

        // Users can access their own files
        if ($fileUpload->user_id === $user->id) {
            return true;
        }

        // Admins can access all files (if you have admin roles)
        if ($user->role === 'admin') {
            return true;
        }

        return false;
    }

    /**
     * Download a file
     */
    public function download(Request $request, string $path): Response
    {
        try {
            // Decrypt the file path
            $filePath = decrypt($path);
            
            // Get file upload record
            $fileUpload = FileUpload::where('file_path', $filePath)->first();
            
            if (!$fileUpload) {
                abort(404, 'File not found');
            }

            // Check if user has permission to access this file
            if (!$this->canAccessFile($fileUpload)) {
                abort(403, 'Access denied');
            }

            // Check virus scan status
            if ($fileUpload->virus_scan_status === 'infected') {
                abort(403, 'File is infected and cannot be downloaded');
            }

            // Get file content
            $content = $this->fileUploadService->getFileContent($filePath);
            
            if (!$content) {
                abort(404, 'File content not found');
            }

            // Return download response
            return response($content)
                ->header('Content-Type', $fileUpload->mime_type)
                ->header('Content-Disposition', 'attachment; filename="' . $fileUpload->original_name . '"')
                ->header('Content-Length', strlen($content));
                
        } catch (\Exception $e) {
            abort(404, 'Invalid file path');
        }
    }
}
