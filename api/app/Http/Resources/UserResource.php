<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->full_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'national_id' => $this->national_id,
            'date_of_birth' => $this->date_of_birth?->format('Y-m-d'),
            'address' => $this->address,
            'avatar_url' => $this->avatar_url,
            'national_id_front_url' => $this->national_id_front_url,
            'national_id_back_url' => $this->national_id_back_url,
            'identity_verification_status' => $this->identity_verification_status,
            'identity_verified_at' => $this->identity_verified_at?->toISOString(),
            'status' => $this->status,
            'email_verified' => $this->email_verified,
            'email_verified_at' => $this->email_verified_at?->toISOString(),
            'last_login_at' => $this->last_login_at?->toISOString(),
            'login_count' => $this->login_count,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            'profile' => $this->whenLoaded('profile', function () {
                return [
                    'church_belong' => $this->profile->church_belong,
                    'father_of_confession' => $this->profile->father_of_confession,
                    'confession_church' => $this->profile->confession_church,
                    'payment_method' => $this->profile->payment_method,
                    'payment_status' => $this->profile->payment_status,
                    'registration_step' => $this->profile->registration_step,
                ];
            }),
        ];
    }
}
