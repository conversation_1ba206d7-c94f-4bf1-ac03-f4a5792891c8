# Authentication API Documentation

This document provides comprehensive documentation for the Mashora Courses authentication API endpoints.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Most endpoints require authentication using Bearer tokens. Include the token in the Authorization header:

```
Authorization: Bearer {your_token_here}
```

## Rate Limiting

-   **Authentication endpoints**: 5 requests per minute per IP
-   **Password reset endpoints**: 3 requests per hour per IP
-   **Email verification endpoints**: 3 requests per hour per IP
-   **General API endpoints**: 60 requests per minute per user/IP

## Error Responses

All endpoints return consistent error responses:

```json
{
    "message": "Error description",
    "errors": {
        "field_name": ["Validation error message"]
    }
}
```

Common HTTP status codes:

-   `200` - Success
-   `201` - Created
-   `400` - Bad Request
-   `401` - Unauthorized
-   `403` - Forbidden
-   `422` - Validation Error
-   `429` - Too Many Requests

---

## Endpoints

### 1. User Registration

**POST** `/auth/register`

Register a new user account with identity verification documents.

#### Content Type

```
Content-Type: multipart/form-data
```

#### Request Body (Form Data)

```
first_name: "John"
last_name: "Doe"
email: "<EMAIL>"
phone: "+************"
password: "password123"
password_confirmation: "password123"
national_id: "**************"
date_of_birth: "1990-01-01"
address: "123 Main St, Cairo, Egypt"
church_belong: "St. Mark Church"
father_of_confession: "Father John"
confession_church: "St. Mark Church"
avatar: [FILE] (optional)
national_id_front: [FILE] (required)
national_id_back: [FILE] (required)
```

#### Required Fields

-   `first_name` (string, max: 255)
-   `last_name` (string, max: 255)
-   `email` (string, email, unique)
-   `password` (string, confirmed, min: 8)
-   `national_id_front` (file, image, JPEG/PNG, max: 5MB, min: 200x200px)
-   `national_id_back` (file, image, JPEG/PNG, max: 5MB, min: 200x200px)

#### Optional Fields

-   `phone` (string, max: 20, unique)
-   `national_id` (string, max: 20, unique)
-   `date_of_birth` (date, before: today)
-   `address` (string, max: 500)
-   `church_belong` (string, max: 255)
-   `father_of_confession` (string, max: 255)
-   `confession_church` (string, max: 255)
-   `avatar` (file, image, JPEG/PNG, max: 5MB)

#### File Upload Requirements

-   **Supported formats**: JPEG, PNG
-   **Maximum file size**: 5MB per file
-   **Image dimensions**:
    -   National ID documents: minimum 200x200 pixels, maximum 2048x2048 pixels
    -   Avatar: maximum 2048x2048 pixels (no minimum)
-   **Security**: Files are scanned for viruses and malicious content
-   **Storage**: Files are stored securely with encrypted access URLs

#### Success Response (201)

```json
{
    "message": "Registration successful. Please check your email to verify your account.",
    "user": {
        "id": 1,
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+************",
        "national_id": "**************",
        "date_of_birth": "1990-01-01",
        "address": "123 Main St, Cairo, Egypt",
        "avatar_url": "https://api.example.com/files/serve/encrypted_path_here",
        "national_id_front_url": "https://api.example.com/files/serve/encrypted_path_here",
        "national_id_back_url": "https://api.example.com/files/serve/encrypted_path_here",
        "identity_verification_status": "pending",
        "identity_verified_at": null,
        "status": "active",
        "email_verified": false,
        "email_verified_at": null,
        "last_login_at": null,
        "login_count": 0,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z",
        "profile": {
            "church_belong": "St. Mark Church",
            "father_of_confession": "Father John",
            "confession_church": "St. Mark Church",
            "payment_method": null,
            "payment_status": null,
            "registration_step": 1
        }
    },
    "token": "1|abc123...",
    "token_type": "Bearer"
}
```

---

### 2. User Login

**POST** `/auth/login`

Authenticate a user and receive an access token.

#### Request Body

```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### Required Fields

-   `email` (string, email)
-   `password` (string)

#### Success Response (200)

```json
{
    "message": "Login successful",
    "user": {
        "id": 1,
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+************",
        "status": "active",
        "email_verified": true,
        "email_verified_at": "2024-01-01T00:00:00.000000Z",
        "last_login_at": "2024-01-01T12:00:00.000000Z",
        "login_count": 5,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z",
        "profile": {
            "church_belong": "St. Mark Church",
            "father_of_confession": "Father John",
            "confession_church": "St. Mark Church",
            "payment_method": "credit_card",
            "payment_status": "paid",
            "registration_step": 5
        }
    },
    "token": "2|def456...",
    "token_type": "Bearer"
}
```

#### Error Response (422)

```json
{
    "message": "The provided credentials are incorrect.",
    "errors": {
        "email": ["The provided credentials are incorrect."]
    }
}
```

---

### 3. User Logout

**POST** `/auth/logout`

Logout the current user and revoke the current access token.

#### Headers

```
Authorization: Bearer {token}
```

#### Success Response (200)

```json
{
    "message": "Logged out successfully"
}
```

---

### 4. Logout from All Devices

**POST** `/auth/logout-all`

Logout the user from all devices by revoking all access tokens.

#### Headers

```
Authorization: Bearer {token}
```

#### Success Response (200)

```json
{
    "message": "Logged out from all devices successfully"
}
```

---

### 5. Forgot Password

**POST** `/auth/forgot-password`

Send a password reset link to the user's email.

#### Request Body

```json
{
    "email": "<EMAIL>"
}
```

#### Required Fields

-   `email` (string, email, exists in users table)

#### Success Response (200)

```json
{
    "message": "Password reset link sent to your email"
}
```

#### Error Response (422)

```json
{
    "message": "We could not find a user with that email address.",
    "errors": {
        "email": ["We could not find a user with that email address"]
    }
}
```

---

### 6. Reset Password

**POST** `/auth/reset-password`

Reset the user's password using a valid reset token.

#### Request Body

```json
{
    "email": "<EMAIL>",
    "password": "new_password123",
    "password_confirmation": "new_password123",
    "token": "reset_token_here"
}
```

#### Required Fields

-   `email` (string, email, exists in users table)
-   `password` (string, confirmed, min: 8)
-   `token` (string, valid reset token)

#### Success Response (200)

```json
{
    "message": "Password reset successfully"
}
```

#### Error Response (422)

```json
{
    "message": "This password reset token is invalid.",
    "errors": {
        "email": ["This password reset token is invalid."]
    }
}
```

---

### 7. Email Verification

**GET** `/auth/verify-email/{id}/{hash}`

Verify a user's email address using a signed verification link.

#### URL Parameters

-   `id` (integer) - User ID
-   `hash` (string) - SHA1 hash of the user's email

#### Query Parameters

-   `expires` (timestamp) - Link expiration time
-   `signature` (string) - URL signature for security

#### Success Response (200)

```json
{
    "message": "Email verified successfully"
}
```

#### Error Response (400)

```json
{
    "message": "Invalid verification link"
}
```

---

### 8. Resend Email Verification

**POST** `/auth/resend-verification`

Resend the email verification link to the user.

#### Request Body

```json
{
    "email": "<EMAIL>"
}
```

#### Required Fields

-   `email` (string, email, exists in users table)

#### Success Response (200)

```json
{
    "message": "Verification email sent"
}
```

#### Already Verified Response (200)

```json
{
    "message": "Email already verified"
}
```

---

### 9. Refresh Token

**POST** `/auth/refresh`

Refresh the current access token (revokes current token and issues a new one).

#### Headers

```
Authorization: Bearer {token}
```

#### Success Response (200)

```json
{
    "token": "3|ghi789...",
    "token_type": "Bearer",
    "user": {
        "id": 1,
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "email": "<EMAIL>",
        "status": "active",
        "email_verified": true,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z",
        "profile": {
            "church_belong": "St. Mark Church",
            "registration_step": 5
        }
    }
}
```

---

### 10. Get Current User

**GET** `/auth/me`

Get the currently authenticated user's information.

#### Headers

```
Authorization: Bearer {token}
```

#### Success Response (200)

```json
{
    "user": {
        "id": 1,
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+************",
        "national_id": "**************",
        "date_of_birth": "1990-01-01",
        "address": "123 Main St, Cairo, Egypt",
        "avatar_url": null,
        "status": "active",
        "email_verified": true,
        "email_verified_at": "2024-01-01T00:00:00.000000Z",
        "last_login_at": "2024-01-01T12:00:00.000000Z",
        "login_count": 5,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z",
        "profile": {
            "church_belong": "St. Mark Church",
            "father_of_confession": "Father John",
            "confession_church": "St. Mark Church",
            "payment_method": "credit_card",
            "payment_status": "paid",
            "registration_step": 5
        }
    }
}
```

---

## Security Features

### 1. Rate Limiting

All authentication endpoints are protected by rate limiting to prevent abuse:

-   Login attempts: 5 per minute per IP
-   Registration: 5 per minute per IP
-   Password reset: 3 per hour per IP
-   Email verification: 3 per hour per IP

### 2. Password Security

-   Minimum 8 characters
-   Automatically hashed using Laravel's secure hashing
-   Password confirmation required for registration and reset

### 3. Email Verification

-   Signed URLs with expiration (60 minutes)
-   SHA1 hash verification
-   Automatic verification email on registration

### 4. Token Management

-   Laravel Sanctum for secure API tokens
-   Multiple device support
-   Token revocation on password reset
-   Secure token refresh mechanism

### 5. Input Validation

-   Comprehensive validation rules
-   Unique constraints for email, phone, and national ID
-   Proper error messages in both English and Arabic

### 6. User Status Management

-   Active/inactive/suspended user status
-   Login prevention for non-active users
-   Audit logging for security events

---

## Testing

All authentication endpoints are thoroughly tested with:

-   Unit tests for individual components
-   Feature tests for complete workflows
-   Rate limiting tests
-   Security tests for edge cases
-   Email verification tests
-   Token management tests

Run tests with:

```bash
php artisan test tests/Feature/Auth/
```

---

## Configuration

### Environment Variables

```env
# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mashora_courses
DB_USERNAME=root
DB_PASSWORD=

# Mail Configuration (for email verification and password reset)
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your_username
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Mashora Courses"

# Frontend URL (for password reset redirects)
FRONTEND_URL=http://localhost:3000
```

### Sanctum Configuration

The API uses Laravel Sanctum for authentication. Configuration is in `config/sanctum.php`.

---

## File Upload and Access Endpoints

### 1. Serve File

**GET** `/files/serve/{encrypted_path}`

Serve a file securely with proper access control.

#### Headers

```
Authorization: Bearer {token}
```

#### URL Parameters

-   `encrypted_path` (string) - Encrypted file path obtained from user data

#### Success Response (200)

Returns the file content with appropriate headers:

```
Content-Type: image/jpeg
Content-Disposition: inline; filename="original_filename.jpg"
Cache-Control: private, max-age=3600
X-Content-Type-Options: nosniff
```

#### Error Responses

-   `401` - Unauthorized (not authenticated)
-   `403` - Forbidden (no access to file or file is infected)
-   `404` - File not found

---

### 2. Download File

**GET** `/files/download/{encrypted_path}`

Download a file with attachment headers.

#### Headers

```
Authorization: Bearer {token}
```

#### URL Parameters

-   `encrypted_path` (string) - Encrypted file path obtained from user data

#### Success Response (200)

Returns the file content with download headers:

```
Content-Type: image/jpeg
Content-Disposition: attachment; filename="original_filename.jpg"
Content-Length: {file_size}
```

#### Error Responses

-   `401` - Unauthorized (not authenticated)
-   `403` - Forbidden (no access to file or file is infected)
-   `404` - File not found

---

## File Upload Security Features

### 1. File Validation

-   **File type validation**: Only JPEG and PNG images are allowed
-   **MIME type verification**: File content must match the declared MIME type
-   **File size limits**: Maximum 5MB per file
-   **Image dimension validation**: Minimum and maximum pixel dimensions enforced
-   **Malicious content detection**: Files are scanned for suspicious patterns

### 2. Secure Storage

-   **Private storage**: Files are stored outside the web root
-   **Encrypted access URLs**: File paths are encrypted in URLs
-   **Access control**: Users can only access their own files
-   **Virus scanning**: All files are scanned for malware (placeholder implementation)

### 3. File Processing

-   **Image optimization**: Large images are automatically resized
-   **Quality optimization**: Images are compressed for optimal storage
-   **Checksum generation**: MD5 and SHA256 checksums for integrity verification
-   **Unique naming**: Files are renamed with timestamps and random strings

---

## Error Handling

The API provides consistent error responses with appropriate HTTP status codes and detailed error messages. All validation errors include field-specific messages to help with frontend integration.

### File Upload Error Examples

#### Invalid File Type (422)

```json
{
    "message": "The given data was invalid.",
    "errors": {
        "national_id_front": [
            "The national_id_front must be a JPEG or PNG image."
        ]
    }
}
```

#### File Too Large (422)

```json
{
    "message": "The given data was invalid.",
    "errors": {
        "avatar": ["The avatar must not be larger than 5MB."]
    }
}
```

#### Image Dimensions Invalid (422)

```json
{
    "message": "The given data was invalid.",
    "errors": {
        "national_id_back": [
            "The national_id_back must be at least 200x200 pixels."
        ]
    }
}
```

#### File Upload Failed (422)

```json
{
    "message": "Registration failed: File upload failed",
    "errors": {
        "general": ["File upload failed. Please try again."]
    }
}
```

For support or questions about the authentication API, please refer to the test files in `tests/Feature/Auth/` for comprehensive usage examples.
