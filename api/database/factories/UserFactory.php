<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => fake()->unique()->safeEmail(),
            'phone' => fake()->optional()->phoneNumber(),
            'password' => static::$password ??= Hash::make('password'),
            'national_id' => fake()->optional()->numerify('##############'),
            'date_of_birth' => fake()->optional()->date('Y-m-d', '-18 years'),
            'address' => fake()->optional()->address(),
            'avatar_url' => null,
            'avatar_file_path' => null,
            'national_id_front_file_path' => null,
            'national_id_back_file_path' => null,
            'identity_verification_status' => 'pending',
            'identity_verification_notes' => null,
            'identity_verified_at' => null,
            'status' => 'active',
            'email_verified' => true,
            'email_verified_at' => now(),
            'last_login_at' => null,
            'login_count' => 0,
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified' => false,
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user's identity should be verified.
     */
    public function identityVerified(): static
    {
        return $this->state(fn (array $attributes) => [
            'identity_verification_status' => 'verified',
            'identity_verified_at' => now(),
            'avatar_file_path' => 'uploads/users/1/avatar/avatar_2024-01-01_12-00-00_abc123.jpg',
            'national_id_front_file_path' => 'uploads/users/1/national_id_front/front_2024-01-01_12-00-00_def456.jpg',
            'national_id_back_file_path' => 'uploads/users/1/national_id_back/back_2024-01-01_12-00-00_ghi789.jpg',
        ]);
    }

    /**
     * Indicate that the user's identity should be rejected.
     */
    public function identityRejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'identity_verification_status' => 'rejected',
            'identity_verification_notes' => 'Documents are not clear enough',
            'identity_verified_at' => null,
        ]);
    }
}
