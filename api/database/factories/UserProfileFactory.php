<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserProfile>
 */
class UserProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserProfile::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'church_belong' => fake()->optional()->randomElement([
                'St. Mark Church',
                'St. Mary Church',
                'St. George Church',
                'Holy Family Church',
                'St. Joseph Church',
            ]),
            'father_of_confession' => fake()->optional()->name('male'),
            'confession_church' => fake()->optional()->randomElement([
                'St. Mark Church',
                'St. Mary Church',
                'St. George Church',
                'Holy Family Church',
                'St. Joseph Church',
            ]),
            'payment_method' => fake()->optional()->randomElement([
                'credit_card',
                'bank_transfer',
                'cash',
            ]),
            'payment_status' => fake()->optional()->randomElement([
                'pending',
                'paid',
                'failed',
            ]),
            'registration_step' => fake()->numberBetween(1, 5),
        ];
    }
}
