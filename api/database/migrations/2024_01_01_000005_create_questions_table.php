<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('quiz_id');
            $table->text('text');
            $table->text('text_ar')->nullable(); // Arabic question text
            $table->json('options_json');
            $table->json('options_json_ar')->nullable(); // Arabic options
            $table->string('correct_answer');
            $table->text('explanation')->nullable();
            $table->text('explanation_ar')->nullable(); // Arabic explanation
            $table->enum('difficulty', ['easy', 'medium', 'hard'])->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
