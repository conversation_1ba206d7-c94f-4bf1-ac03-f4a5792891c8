<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_receipts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('transaction_id');
            $table->string('receipt_number');
            $table->decimal('amount', 10, 2);
            $table->string('currency')->default('EGP');
            $table->string('payment_method');
            $table->string('card_last4')->nullable();
            $table->string('card_type')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed'])->default('completed');
            $table->string('subscription_type')->default('full');
            $table->boolean('fees_included')->default(true);
            $table->string('student_name'); // اسم الطالب
            $table->string('course_name'); // اسم الكورس
            $table->string('course_name_ar')->nullable(); // Arabic course name
            $table->text('fees_description')->nullable(); // تفاصيل الرسوم
            $table->timestamp('transaction_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_receipts');
    }
};
