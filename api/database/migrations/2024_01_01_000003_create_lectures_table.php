<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lectures', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('title_en')->nullable();
            $table->string('title_ar')->nullable(); // Arabic title
            $table->integer('duration')->nullable(); // seconds
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable(); // Arabic description
            $table->string('video_url')->nullable(); // Bunny Stream playback URL or ID
            $table->text('transcript')->nullable();
            $table->text('transcript_ar')->nullable(); // Arabic transcript
            $table->integer('order_index')->default(0);
            $table->enum('status', ['draft', 'published', 'archived'])->default('published');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lectures');
    }
};
