<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_profiles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('church_belong')->nullable();
            $table->string('father_of_confession')->nullable();
            $table->string('confession_church')->nullable();
            $table->string('payment_method')->nullable();
            $table->string('payment_status')->nullable(); // synced with latest paid transaction
            $table->integer('registration_step')->nullable(); // multi-step signup progress
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_profiles');
    }
};
