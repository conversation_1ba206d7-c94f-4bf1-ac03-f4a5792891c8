<?php

use App\Http\Controllers\FileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Password reset route for email notifications
Route::get('/password/reset/{token}', function ($token) {
    return redirect(config('app.frontend_url', 'http://localhost:3000') . '/reset-password?token=' . $token);
})->name('password.reset');

// File serving routes (protected)
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/files/serve/{path}', [FileController::class, 'serve'])->name('file.serve');
    Route::get('/files/download/{path}', [FileController::class, 'download'])->name('file.download');
});
